"""
数据库模块初始化文件

该模块负责数据库相关功能的初始化和配置管理。
主要功能包括：
- 数据库连接配置
- 功能开关管理
- 数据库操作接口导出

设计理念：
为 MVP 版本提供灵活的数据库集成方案，支持通过配置开关
控制是否启用数据持久化功能，便于在开发和生产环境中
根据需要选择性启用数据库功能。

使用方式：
- 开发阶段：可关闭数据库功能，使用内存数据进行快速开发
- 生产环境：启用数据库功能，提供完整的数据持久化能力
"""

import os
import logging
from typing import Optional

# 配置日志
logger = logging.getLogger(__name__)

# 数据库功能开关配置
# 通过环境变量控制是否启用 MongoDB 功能
# 设置 ENABLE_DATABASE=true 启用数据库功能
ENABLE_DATABASE = os.getenv("ENABLE_DATABASE", "false").lower() == "true"

# 数据库实例
_mongodb_instance: Optional['MongoDB'] = None

def get_database():
    """
    获取数据库实例
    
    根据配置开关返回数据库实例或 None。
    采用延迟初始化模式，只在需要时创建数据库连接。
    
    Returns:
        MongoDB | None: 如果启用数据库功能返回 MongoDB 实例，否则返回 None
    
    Example:
        >>> db = get_database()
        >>> if db:
        ...     db.save_prompt("user123", "需求描述", "zh")
        ... else:
        ...     print("数据库功能未启用")
    """
    global _mongodb_instance
    
    if not ENABLE_DATABASE:
        logger.info("数据库功能未启用，跳过数据库操作")
        return None
    
    if _mongodb_instance is None:
        try:
            from .mongodb import MongoDB
            _mongodb_instance = MongoDB()
            logger.info("数据库实例初始化成功")
        except Exception as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            _mongodb_instance = None
    
    return _mongodb_instance

def init_database():
    """
    初始化数据库连接
    
    在应用启动时调用，预先建立数据库连接。
    如果数据库功能未启用或连接失败，不会影响应用正常启动。
    
    Returns:
        bool: 初始化成功返回 True，失败或未启用返回 False
    """
    if not ENABLE_DATABASE:
        logger.info("数据库功能未启用")
        return False
    
    db = get_database()
    return db is not None

def close_database():
    """
    关闭数据库连接
    
    在应用关闭时调用，确保数据库连接正确释放。
    """
    global _mongodb_instance
    
    if _mongodb_instance and hasattr(_mongodb_instance, 'client'):
        try:
            _mongodb_instance.client.close()
            logger.info("数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {str(e)}")
        finally:
            _mongodb_instance = None

# 导出数据库相关接口
__all__ = [
    'get_database',
    'init_database', 
    'close_database',
    'ENABLE_DATABASE'
]

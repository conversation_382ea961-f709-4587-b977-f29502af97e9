"""
需求文档生成路由模块

该模块负责处理需求文档生成相关的 API 端点。
主要功能包括：
- 接收用户选择的案例 ID
- 调用 AI 服务生成详细的需求文档内容
- 将文档内容转换为 PDF 格式
- 返回可下载的文档 URL

API 端点：
- POST /api/generate-demand: 根据选中案例生成需求文档

数据流程：
案例选择 → AI 生成文档内容 → PDF 转换 → 文档 URL 返回

该模块是 FindU 系统的最终输出环节，将用户的需求转化为可交付的文档。
"""

from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Optional
import logging
from ..services.document_service import document_service, DocumentFormat
from ..services.ai_service import get_ai_service

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器实例，用于组织文档生成相关的 API 端点
router = APIRouter()

class CaseSelection(BaseModel):
    """
    案例选择请求模型

    定义客户端请求生成需求文档时需要的参数结构。
    包含完整的案例信息，用于 AI 服务生成详细的需求文档。

    Attributes:
        case_id (int): 用户选中的案例唯一标识符（用于文件命名）
        case_title (str): 案例标题
        case_description (str): 案例描述
        case_details (List[str]): 案例详细需求列表
        locale (str): 目标语言代码，默认为英文 "en"，支持 "zh" 中文
        format (str): 文档格式，支持 "pdf", "docx", "txt"，默认为 "pdf"
    """
    case_id: int
    case_title: str
    case_description: str
    case_details: List[str]
    locale: str = "en"
    format: str = "pdf"

@router.post("/generate-demand")
async def generate_demand(request: CaseSelection):
    """
    生成需求文档 API 端点

    根据用户选择的案例信息，调用 AI 服务生成详细的需求文档，
    然后将文档内容转换为指定格式并流式返回。

    处理流程：
    1. 接收完整的案例信息（标题、描述、详细需求、格式）
    2. 调用 AI 服务基于案例信息生成 Markdown 格式的文档内容
    3. 将 Markdown 内容转换为指定格式的文档
    4. 流式返回文档数据，避免后端文件存储

    Args:
        request (CaseSelection): 包含完整案例信息、语言偏好和格式的请求对象

    Returns:
        StreamingResponse: 流式文档响应

    Raises:
        HTTPException: 当 AI 服务调用失败、文档生成失败或其他错误发生时抛出异常

    Example:
        请求体:
        {
            "case_id": 1,
            "case_title": "电子商务网站",
            "case_description": "功能完整的在线购物平台",
            "case_details": ["用户注册登录", "商品展示目录", "购物车功能"],
            "locale": "zh",
            "format": "pdf"
        }

        响应: 流式文档数据
    """
    try:
        logger.info(f"开始生成需求文档，案例ID: {request.case_id}, 格式: {request.format}")

        # 验证格式
        format_lower = request.format.lower()
        if format_lower == "pdf":
            format_type = DocumentFormat.PDF
        elif format_lower == "docx":
            format_type = DocumentFormat.DOCX
        elif format_lower == "txt":
            format_type = DocumentFormat.TXT
        else:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的格式: {request.format}。支持的格式: {document_service.get_supported_formats()}"
            )

        # 检查格式是否支持
        if not document_service.is_format_supported(format_type):
            raise HTTPException(
                status_code=400,
                detail=f"格式 {request.format} 当前不可用，请检查相关依赖是否已安装"
            )

        # 将请求数据转换为 CaseData 对象
        from ..services.ai_service import CaseData
        case_data = CaseData(
            id=request.case_id,
            title=request.case_title,
            description=request.case_description,
            details=request.case_details
        )

        # 调用 AI 服务生成 Markdown 格式的需求文档内容
        content = await get_ai_service().generate_document(case_data, request.locale)

        # 生成文档标题
        title = f"{request.case_title} - 需求文档"

        # 生成文档
        document_data, mime_type = document_service.generate_document(
            content=content,
            format_type=format_type,
            title=title
        )

        # 生成文件名
        filename = document_service.get_filename(title, format_type)

        logger.info(f"需求文档生成成功，案例ID: {request.case_id}, 格式: {request.format}, 大小: {len(document_data)} 字节")

        # 创建流式响应
        def generate_stream():
            yield document_data

        return StreamingResponse(
            generate_stream(),
            media_type=mime_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Length": str(len(document_data))
            }
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"需求文档生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"需求文档生成失败: {str(e)}")

@router.get("/formats")
async def get_supported_formats():
    """
    获取支持的文档格式

    Returns:
        dict: 支持的格式列表和详细信息
    """
    supported_formats = document_service.get_supported_formats()

    format_info = {
        "pdf": {
            "name": "PDF",
            "description": "便携式文档格式，适合打印和分享",
            "mime_type": "application/pdf",
            "available": document_service.is_format_supported(DocumentFormat.PDF)
        },
        "docx": {
            "name": "Word文档",
            "description": "Microsoft Word格式，适合编辑和协作",
            "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "available": document_service.is_format_supported(DocumentFormat.DOCX)
        },
        "txt": {
            "name": "纯文本",
            "description": "纯文本格式，兼容性最好",
            "mime_type": "text/plain",
            "available": document_service.is_format_supported(DocumentFormat.TXT)
        }
    }

    return {
        "supported_formats": supported_formats,
        "format_details": {fmt: format_info[fmt] for fmt in supported_formats if fmt in format_info},
        "message": f"当前支持 {len(supported_formats)} 种文档格式"
    }

class DocumentRequest(BaseModel):
    """直接文档生成请求模型"""
    content: str
    title: Optional[str] = "FindU 项目需求文档"
    format: str = "pdf"

@router.post("/documents/generate")
async def generate_document_direct(request: DocumentRequest):
    """
    直接生成文档 API 端点（不依赖AI服务）

    直接将提供的内容转换为指定格式的文档并流式返回。

    Args:
        request: 文档生成请求，包含内容、标题和格式

    Returns:
        StreamingResponse: 流式文档响应

    Raises:
        HTTPException: 当文档生成失败时抛出异常
    """
    try:
        logger.info(f"开始直接生成文档，格式: {request.format}, 标题: {request.title}")

        # 验证格式
        format_lower = request.format.lower()
        if format_lower == "pdf":
            format_type = DocumentFormat.PDF
        elif format_lower == "docx":
            format_type = DocumentFormat.DOCX
        elif format_lower == "txt":
            format_type = DocumentFormat.TXT
        else:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的格式: {request.format}。支持的格式: {document_service.get_supported_formats()}"
            )

        # 检查格式是否支持
        if not document_service.is_format_supported(format_type):
            raise HTTPException(
                status_code=400,
                detail=f"格式 {request.format} 当前不可用，请检查相关依赖是否已安装"
            )

        # 生成文档
        document_data, mime_type = document_service.generate_document(
            content=request.content,
            format_type=format_type,
            title=request.title
        )

        # 生成文件名
        filename = document_service.get_filename(request.title, format_type)

        logger.info(f"直接文档生成成功，格式: {request.format}, 大小: {len(document_data)} 字节")

        # 创建流式响应
        def generate_stream():
            yield document_data

        return StreamingResponse(
            generate_stream(),
            media_type=mime_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Length": str(len(document_data))
            }
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"直接文档生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"直接文档生成失败: {str(e)}")
/**
 * 根布局组件
 *
 * 该组件为整个应用程序提供统一的页面布局结构，包括：
 * - HTML 文档结构和语言设置
 * - 顶部导航栏（包含应用名称和语言切换）
 * - 主要内容区域
 * - 底部页脚信息
 *
 * 功能特性：
 * - 国际化支持
 * - 响应式设计
 * - 语言切换功能
 * - 统一的视觉风格
 */

import LayoutClient from '@/components/LayoutClient';
import '@/styles/globals.css';

/**
 * 根布局组件
 *
 * 为所有页面提供一致的布局结构和导航功能。
 * 根据当前语言设置显示相应的内容和样式。
 *
 * @param children - 页面的主要内容
 * @param params - 路由参数，包含当前语言设置
 * @returns React 函数组件
 */
export default async function RootLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>
}) {
  // 获取路由参数
  const { locale } = await params;

  return (
    <html lang={locale}>
      <body className="antialiased bg-gray-50">
        <LayoutClient locale={locale}>
          {children}
        </LayoutClient>
      </body>
    </html>
  );
}
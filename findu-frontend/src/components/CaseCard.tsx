/**
 * 案例卡片组件
 *
 * 该组件用于展示单个需求案例的详细信息，包括标题、描述和详细要求。
 * 支持选中状态的视觉反馈，用户可以点击选择特定的案例。
 *
 * 功能特性：
 * - 案例信息展示（标题、描述、详细要求列表）
 * - 选中状态的视觉反馈
 * - 国际化支持
 * - 响应式设计
 * - 交互式选择功能
 */

import { Case } from '@/lib/types';
import { useTranslation } from '@/lib/i18n';
import { Card, CardContent, CardFooter } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

/**
 * CaseCard 组件的属性接口
 */
interface CaseCardProps {
  /** 要展示的案例数据对象 */
  caseItem: Case;
  /** 当前案例是否被选中 */
  isSelected: boolean;
  /** 用户选择该案例时的回调函数 */
  onSelect: () => void;
  /** 当前语言环境 */
  locale: string;
}

/**
 * 案例卡片组件
 *
 * 以卡片形式展示单个需求案例的完整信息，包括标题、描述和详细要求列表。
 * 当案例被选中时，会显示不同的视觉样式以提供用户反馈。
 *
 * @param props - 组件属性
 * @returns React 函数组件
 */
export default function CaseCard({ caseItem, isSelected, onSelect, locale }: CaseCardProps) {
  // 获取国际化翻译函数
  const { t } = useTranslation(locale);

  return (
    <Card
      variant={isSelected ? "elevated" : "default"}
      clickable={true}
      hover={!isSelected}
      className={`transition-all duration-300 transform hover:scale-105 ${
        isSelected
          ? 'ring-2 ring-cyan-400 ring-offset-2 ring-offset-slate-900 bg-gradient-to-br from-slate-700/90 to-slate-800/90 shadow-2xl shadow-cyan-500/25 border-cyan-400/30'
          : 'hover:shadow-2xl hover:shadow-blue-500/20 bg-slate-800/80 border-slate-600/50 backdrop-blur-xl'
      }`}
      onClick={onSelect}
    >
      <CardContent className="p-6">
        {/* 案例标题 */}
        <h3 className="text-xl font-bold text-white mb-3 leading-tight flex items-center">
          <svg className="w-5 h-5 mr-2 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          {caseItem.title}
        </h3>

        {/* 案例描述 */}
        <p className="text-gray-300 mb-4 leading-relaxed">
          {caseItem.description}
        </p>

        {/* 详细要求列表 */}
        <div className="space-y-2">
          <h4 className="text-sm font-semibold text-cyan-300 mb-2 flex items-center">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            详细要求：
          </h4>
          <ul className="space-y-1">
            {caseItem.details.map((detail, idx) => (
              <li key={idx} className="flex items-start text-sm text-gray-300">
                <span className="inline-block w-1.5 h-1.5 bg-cyan-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                <span>{detail}</span>
              </li>
            ))}
          </ul>
        </div>
      </CardContent>

      <CardFooter className="px-6 py-4">
        <Button
          variant={isSelected ? "primary" : "outline"}
          size="md"
          className={`w-full ${isSelected
            ? 'bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 shadow-lg shadow-cyan-500/25'
            : 'border-cyan-400/50 text-cyan-300 hover:bg-cyan-500/10 hover:border-cyan-400'
          }`}
          onClick={(e) => {
            e.stopPropagation();
            onSelect();
          }}
        >
          {isSelected ? (
            <span className="flex items-center">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              已选择
            </span>
          ) : (
            t('select_case')
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
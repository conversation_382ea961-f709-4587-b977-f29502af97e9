/**
 * 文档预览组件
 *
 * 该组件用于预览和下载生成的需求文档。提供内嵌的文档预览功能
 * 和便捷的下载链接，让用户可以在生成文档后立即查看和获取文件。
 *
 * 功能特性：
 * - 内嵌 iframe 文档预览
 * - 文档下载功能
 * - 国际化支持
 * - 响应式设计
 * - 用户友好的界面
 */

import { useTranslation } from '@/lib/i18n';
import { Button } from '@/components/ui/Button';

/**
 * DocumentPreview 组件的属性接口
 */
interface DocumentPreviewProps {
  /** 文档的 URL 地址 */
  documentUrl: string;
  /** 当前语言环境 */
  locale: string;
}

/**
 * 文档预览组件
 *
 * 提供生成文档的预览和下载功能。使用 iframe 嵌入文档内容，
 * 同时提供下载链接让用户可以保存文档到本地。
 *
 * @param props - 组件属性
 * @returns React 函数组件
 */
export default function DocumentPreview({ documentUrl, locale }: DocumentPreviewProps) {
  // 获取国际化翻译函数
  const { t } = useTranslation(locale);

  return (
    <div className="space-y-6">
      {/* 预览标题 */}
      <div className="text-center">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          {t('document_preview')}
        </h3>
        <p className="text-gray-600">
          您的需求文档已生成完成，可以预览和下载
        </p>
      </div>

      {/* 文档预览 iframe */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <iframe
          src={documentUrl}
          className="w-full h-[800] border-0"
          title="Demand Document Preview"
        />
      </div>

      {/* 下载按钮 */}
      <div className="flex justify-center">
        <Button
          variant="primary"
          size="lg"
          className="px-8 py-3"
          onClick={() => window.open(documentUrl, '_blank')}
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          {t('download_document')}
        </Button>
      </div>
    </div>
  );
}
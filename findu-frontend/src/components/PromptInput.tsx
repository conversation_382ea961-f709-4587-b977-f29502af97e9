/**
 * 提示词输入组件
 *
 * 该组件提供一个多行文本输入框，用于用户输入需求描述。
 * 支持国际化、禁用状态和实时输入处理。
 *
 * 功能特性：
 * - 多行文本输入
 * - 动态占位符文本
 * - 禁用状态支持
 * - 响应式设计
 * - 实时输入值变化处理
 */

import React from 'react';
import { Textarea } from '@/components/ui/Textarea';

/**
 * PromptInput 组件的属性接口
 */
interface PromptInputProps {
  /** 当前输入的提示词内容 */
  prompt: string;
  /** 输入内容变化时的回调函数 */
  onChange: (value: string) => void;
  /** 输入框的占位符文本 */
  placeholder?: string;
  /** 是否禁用输入框 */
  disabled?: boolean;
}

/**
 * 提示词输入组件
 *
 * 提供一个用户友好的多行文本输入界面，用于收集用户的需求描述。
 * 组件会实时响应用户输入并通过回调函数通知父组件。
 *
 * @param props - 组件属性
 * @returns React 函数组件
 */
export default function PromptInput({
  prompt,
  onChange,
  placeholder = "请输入您的需求描述...",
  disabled = false
}: PromptInputProps) {

  /**
   * 处理输入框内容变化
   *
   * 当用户在输入框中输入内容时，此函数会被调用，
   * 并将新的输入值传递给父组件。
   *
   * @param event - 输入事件对象
   */
  const handleInputChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = event.target.value;
    onChange(newValue);
  };

  return (
    <div className="w-full">
      <div className="relative">
        <div className="relative">
          <Textarea
            value={prompt}
            onChange={handleInputChange}
            placeholder={placeholder}
            disabled={disabled}
            autoResize={false}
            showCount={false} // 关闭默认的字符计数显示
            maxLength={2000}
            size="lg"
            helperText="请详细描述您的需求，我们将为您生成相关的案例建议"
            className="min-h-[140px] text-lg leading-relaxed bg-slate-800/50 border-purple-600/30 text-white placeholder:text-purple-200/60 focus:border-purple-400 focus:ring-purple-400/20"
          />
          
          {/* 显示剩余字符数 */}
          <div className="absolute bottom-2 right-4 text-sm text-gray-400">
            剩余字符: {2000 - prompt.length}
          </div>
        </div>


        {/* 装饰性图标 */}
        <div className="absolute top-12 right-4 text-purple-300/60 pointer-events-none">
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        </div>
      </div>

      {/* 提示文本 */}
      <div className="mt-4 flex items-center justify-between text-sm text-purple-200/80">
        <div className="flex items-center space-x-6">
          <span className="flex items-center text-cyan-300">
            <svg className="w-4 h-4 mr-1 text-cyan-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            支持中英文输入
          </span>
          <span className="flex items-center text-blue-300">
            <svg className="w-4 h-4 mr-1 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            越详细越准确
          </span>
          <span className="flex items-center text-purple-300">
            <svg className="w-4 h-4 mr-1 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
            </svg>
            AI智能分析
          </span>
        </div>
      </div>
    </div>
  );
}
/**
 * 输入框组件库
 * 
 * 提供统一的输入框样式和交互效果，支持多种类型和状态。
 * 遵循现代化设计原则，具有良好的可访问性和用户体验。
 * 
 * 功能特性：
 * - 多种输入框类型（text, email, password等）
 * - 多种尺寸（sm, md, lg）
 * - 错误状态支持
 * - 禁用状态支持
 * - 标签和帮助文本支持
 * - 完整的键盘导航支持
 */

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * 输入框组件属性接口
 */
interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  /** 输入框尺寸 */
  size?: 'sm' | 'md' | 'lg';
  /** 是否有错误 */
  error?: boolean;
  /** 标签文本 */
  label?: string;
  /** 帮助文本 */
  helperText?: string;
  /** 错误信息 */
  errorMessage?: string;
}

/**
 * 输入框尺寸样式映射
 */
const inputSizes = {
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-4 py-2 text-base',
  lg: 'px-4 py-3 text-lg',
};

/**
 * 输入框组件
 * 
 * 提供一致的输入框样式和交互效果。支持多种尺寸和状态，
 * 具有良好的可访问性和用户体验。
 * 
 * @param props - 输入框属性
 * @returns React 函数组件
 */
export const Input: React.FC<InputProps> = ({
  size = 'md',
  error = false,
  label,
  helperText,
  errorMessage,
  className,
  id,
  ...props
}) => {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className="w-full">
      {/* 标签 */}
      {label && (
        <label
          htmlFor={inputId}
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          {label}
        </label>
      )}

      {/* 输入框 */}
      <input
        id={inputId}
        className={cn(
          // 基础样式
          'w-full rounded-lg border transition-all duration-200',
          'focus:outline-none focus:ring-2 focus:ring-offset-1',
          'disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50',
          // 尺寸样式
          inputSizes[size],
          // 状态样式
          error
            ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
            : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500',
          // 自定义样式
          className
        )}
        {...props}
      />

      {/* 帮助文本或错误信息 */}
      {(helperText || errorMessage) && (
        <p
          className={cn(
            'mt-1 text-sm',
            error ? 'text-red-600' : 'text-gray-500'
          )}
        >
          {error ? errorMessage : helperText}
        </p>
      )}
    </div>
  );
};

export default Input;

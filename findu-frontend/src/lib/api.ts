/**
 * API 客户端库
 *
 * 该文件提供与后端 API 服务通信的客户端函数。
 * 包含案例生成和需求文档生成的 API 调用功能，
 * 以及相关的 TypeScript 类型定义。
 *
 * 功能特性：
 * - 类型安全的 API 调用
 * - 错误处理机制
 * - 支持国际化参数
 * - RESTful API 接口封装
 */

import { Case } from './types';

/**
 * 生成案例请求参数接口
 * 定义调用案例生成 API 时需要的参数
 */
export interface GenerateCasesRequest {
  /** 用户输入的需求提示词 */
  prompt: string;
  /** 目标语言代码 */
  locale: string;
}


// class CaseData:
//     """案例数据结构"""
//     id: int
//     title: str
//     description: str
//     details: List[str]

/**
 * 生成需求文档请求参数接口
 * 定义调用需求文档生成 API 时需要的参数
 */
export interface GenerateDemandRequest {
  /** 选中的案例 ID */
  case_id: number;
  /** 选中的案例题目 */
  case_title: string;
  /** 选中的案例描述 */
  case_description:string;
  /** 选中的案例简单细节 */
  case_details: string[];
  /** 目标语言代码 */
  locale: string;
  /** 文档格式，默认为 PDF */
  format?: string;
}

/**
 * 生成需求文档响应接口
 * 定义需求文档生成 API 的响应格式
 */
export interface GenerateDemandResponse {
  /** base64编码的文档数据 */
  documentData: string;
  /** 文件名 */
  filename: string;
  /** MIME类型 */
  mimeType: string;
  /** 文件大小（字节） */
  size: number;
  /** 文档格式 */
  format: string;
}

/**
 * 生成需求案例
 *
 * 根据用户输入的提示词，调用后端 AI 服务生成多个相关的需求案例。
 * 返回的案例包含标题、描述和详细要求列表。
 *
 * @param request - 包含提示词和语言设置的请求参数
 * @returns Promise，解析为包含案例数组的响应对象
 * @throws 当 API 调用失败时抛出错误
 */
export async function generateCases(request: GenerateCasesRequest): Promise<{ cases: Case[] }> {
  const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/generate-cases`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(request),
  });

  // 检查响应状态，如果失败则抛出错误
  if (!response.ok) {
    throw new Error('Failed to generate cases');
  }

  return response.json();
}

/**
 * 生成需求文档
 *
 * 根据用户选择的案例，调用后端服务生成详细的需求文档。
 * 返回包含base64编码文档数据的响应对象，用于预览和下载。
 *
 * @param request - 包含案例 ID 和语言设置的请求参数
 * @returns Promise，解析为包含文档数据的响应对象
 * @throws 当 API 调用失败时抛出错误
 */
export async function generateDemand(request: GenerateDemandRequest): Promise<GenerateDemandResponse> {
  // 设置默认格式为 PDF
  const requestWithFormat = {
    ...request,
    format: request.format || 'pdf'
  };

  const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/generate-demand`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(requestWithFormat),
  });

  // 检查响应状态，如果失败则抛出错误
  if (!response.ok) {
    throw new Error('Failed to generate demand document');
  }

  return response.json();
}

/**
 * 下载需求文档
 *
 * 根据用户选择的案例和格式，调用后端服务生成并直接下载文档。
 *
 * @param request - 包含案例 ID、语言设置和格式的请求参数
 * @returns Promise，解析为下载的文件
 * @throws 当 API 调用失败时抛出错误
 */
export async function downloadDemand(request: GenerateDemandRequest): Promise<void> {
  const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/generate-demand-download`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(request),
  });

  // 检查响应状态，如果失败则抛出错误
  if (!response.ok) {
    throw new Error('Failed to download demand document');
  }

  // 获取文件名
  const contentDisposition = response.headers.get('Content-Disposition');
  let filename = 'document';
  if (contentDisposition) {
    const filenameMatch = contentDisposition.match(/filename=(.+)/);
    if (filenameMatch) {
      filename = filenameMatch[1].replace(/"/g, '');
    }
  }

  // 创建下载链接
  const blob = await response.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
}
{"name": "make-cancellable-promise", "version": "2.0.0", "description": "Make any Promise cancellable.", "type": "module", "sideEffects": false, "main": "./dist/index.js", "source": "./src/index.ts", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js", "./*": "./*"}, "scripts": {"build": "tsc --project tsconfig.build.json", "clean": "<PERSON><PERSON><PERSON> dist", "format": "biome format", "lint": "biome lint", "prepack": "yarn clean && yarn build", "test": "yarn lint && yarn tsc && yarn format && yarn unit", "tsc": "tsc", "unit": "vitest"}, "keywords": ["promise", "promise-cancelling"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@biomejs/biome": "1.9.0", "husky": "^9.0.0", "rimraf": "^6.0.0", "typescript": "^5.5.2", "vitest": "^3.0.5"}, "publishConfig": {"access": "public", "provenance": true}, "files": ["dist", "src"], "repository": {"type": "git", "url": "git+https://github.com/wojtekmaj/make-cancellable-promise.git"}, "funding": "https://github.com/wojtekmaj/make-cancellable-promise?sponsor=1", "packageManager": "yarn@4.3.1"}
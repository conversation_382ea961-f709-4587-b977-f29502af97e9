{"name": "merge-refs", "version": "2.0.0", "description": "A function that merges React refs into one.", "type": "module", "sideEffects": false, "main": "./dist/index.js", "source": "./src/index.ts", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js", "./*": "./*"}, "scripts": {"build": "tsc --project tsconfig.build.json", "clean": "<PERSON><PERSON><PERSON> dist", "format": "biome format", "lint": "biome lint", "prepack": "yarn clean && yarn build", "test": "yarn lint && yarn tsc && yarn format && yarn unit", "tsc": "tsc", "unit": "vitest"}, "keywords": ["react", "react ref", "react refs", "merge"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@biomejs/biome": "1.9.0", "@testing-library/dom": "^10.0.0", "@testing-library/react": "^16.0.0", "@types/react": "*", "happy-dom": "^15.10.2", "husky": "^9.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^6.0.0", "typescript": "^5.5.2", "vitest": "^3.0.5"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "publishConfig": {"access": "public", "provenance": true}, "files": ["dist", "src"], "repository": {"type": "git", "url": "git+https://github.com/wojtekmaj/merge-refs.git"}, "funding": "https://github.com/wojtekmaj/merge-refs?sponsor=1", "packageManager": "yarn@4.3.1"}
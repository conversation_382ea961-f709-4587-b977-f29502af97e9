/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2024 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */
/**
 * pdfjsVersion = 5.3.31
 * pdfjsBuild = 47ad820d9
 */
"object"!=typeof process||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&process.type;const e={ERRORS:0,WARNINGS:1,INFOS:5};let t=e.WARNINGS;function setVerbosityLevel(e){Number.isInteger(e)&&(t=e)}function getVerbosityLevel(){return t}function info(n){t>=e.INFOS&&console.log(`Info: ${n}`)}function util_warn(n){t>=e.WARNINGS&&console.log(`Warning: ${n}`)}function unreachable(e){throw new Error(e)}function shadow(e,t,n,r=!1){Object.defineProperty(e,t,{value:n,enumerable:!r,configurable:!0,writable:!1});return n}const n=function BaseExceptionClosure(){function BaseException(e,t){this.message=e;this.name=t}BaseException.prototype=new Error;BaseException.constructor=BaseException;return BaseException}();class FormatError extends n{constructor(e){super(e,"FormatError")}}function bytesToString(e){"object"==typeof e&&void 0!==e?.length||unreachable("Invalid argument for bytesToString");const t=e.length,n=8192;if(t<n)return String.fromCharCode.apply(null,e);const r=[];for(let i=0;i<t;i+=n){const s=Math.min(i+n,t),o=e.subarray(i,s);r.push(String.fromCharCode.apply(null,o))}return r.join("")}class util_FeatureTest{static get isLittleEndian(){return shadow(this,"isLittleEndian",function isLittleEndian(){const e=new Uint8Array(4);e[0]=1;return 1===new Uint32Array(e.buffer,0,1)[0]}())}static get isEvalSupported(){return shadow(this,"isEvalSupported",function isEvalSupported(){try{new Function("");return!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return shadow(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get isImageDecoderSupported(){return shadow(this,"isImageDecoderSupported","undefined"!=typeof ImageDecoder)}static get platform(){const{platform:e,userAgent:t}=navigator;return shadow(this,"platform",{isAndroid:t.includes("Android"),isLinux:e.includes("Linux"),isMac:e.includes("Mac"),isWindows:e.includes("Win"),isFirefox:t.includes("Firefox")})}static get isCSSRoundSupported(){return shadow(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}}const r=Array.from(Array(256).keys(),(e=>e.toString(16).padStart(2,"0")));class util_Util{static makeHexColor(e,t,n){return`#${r[e]}${r[t]}${r[n]}`}static scaleMinMax(e,t){let n;if(e[0]){if(e[0]<0){n=t[0];t[0]=t[2];t[2]=n}t[0]*=e[0];t[2]*=e[0];if(e[3]<0){n=t[1];t[1]=t[3];t[3]=n}t[1]*=e[3];t[3]*=e[3]}else{n=t[0];t[0]=t[1];t[1]=n;n=t[2];t[2]=t[3];t[3]=n;if(e[1]<0){n=t[1];t[1]=t[3];t[3]=n}t[1]*=e[1];t[3]*=e[1];if(e[2]<0){n=t[0];t[0]=t[2];t[2]=n}t[0]*=e[2];t[2]*=e[2]}t[0]+=e[4];t[1]+=e[5];t[2]+=e[4];t[3]+=e[5]}static transform(e,t){return[e[0]*t[0]+e[2]*t[1],e[1]*t[0]+e[3]*t[1],e[0]*t[2]+e[2]*t[3],e[1]*t[2]+e[3]*t[3],e[0]*t[4]+e[2]*t[5]+e[4],e[1]*t[4]+e[3]*t[5]+e[5]]}static applyTransform(e,t,n=0){const r=e[n],i=e[n+1];e[n]=r*t[0]+i*t[2]+t[4];e[n+1]=r*t[1]+i*t[3]+t[5]}static applyTransformToBezier(e,t,n=0){const r=t[0],i=t[1],s=t[2],o=t[3],a=t[4],c=t[5];for(let t=0;t<6;t+=2){const l=e[n+t],h=e[n+t+1];e[n+t]=l*r+h*s+a;e[n+t+1]=l*i+h*o+c}}static applyInverseTransform(e,t){const n=e[0],r=e[1],i=t[0]*t[3]-t[1]*t[2];e[0]=(n*t[3]-r*t[2]+t[2]*t[5]-t[4]*t[3])/i;e[1]=(-n*t[1]+r*t[0]+t[4]*t[1]-t[5]*t[0])/i}static axialAlignedBoundingBox(e,t,n){const r=t[0],i=t[1],s=t[2],o=t[3],a=t[4],c=t[5],l=e[0],h=e[1],f=e[2],u=e[3];let d=r*l+a,m=d,g=r*f+a,p=g,b=o*h+c,w=b,y=o*u+c,C=y;if(0!==i||0!==s){const e=i*l,t=i*f,n=s*h,r=s*u;d+=n;p+=n;g+=r;m+=r;b+=e;C+=e;y+=t;w+=t}n[0]=Math.min(n[0],d,g,m,p);n[1]=Math.min(n[1],b,y,w,C);n[2]=Math.max(n[2],d,g,m,p);n[3]=Math.max(n[3],b,y,w,C)}static inverseTransform(e){const t=e[0]*e[3]-e[1]*e[2];return[e[3]/t,-e[1]/t,-e[2]/t,e[0]/t,(e[2]*e[5]-e[4]*e[3])/t,(e[4]*e[1]-e[5]*e[0])/t]}static singularValueDecompose2dScale(e,t){const n=e[0],r=e[1],i=e[2],s=e[3],o=n**2+r**2,a=n*i+r*s,c=i**2+s**2,l=(o+c)/2,h=Math.sqrt(l**2-(o*c-a**2));t[0]=Math.sqrt(l+h||1);t[1]=Math.sqrt(l-h||1)}static normalizeRect(e){const t=e.slice(0);if(e[0]>e[2]){t[0]=e[2];t[2]=e[0]}if(e[1]>e[3]){t[1]=e[3];t[3]=e[1]}return t}static intersect(e,t){const n=Math.max(Math.min(e[0],e[2]),Math.min(t[0],t[2])),r=Math.min(Math.max(e[0],e[2]),Math.max(t[0],t[2]));if(n>r)return null;const i=Math.max(Math.min(e[1],e[3]),Math.min(t[1],t[3])),s=Math.min(Math.max(e[1],e[3]),Math.max(t[1],t[3]));return i>s?null:[n,i,r,s]}static pointBoundingBox(e,t,n){n[0]=Math.min(n[0],e);n[1]=Math.min(n[1],t);n[2]=Math.max(n[2],e);n[3]=Math.max(n[3],t)}static rectBoundingBox(e,t,n,r,i){i[0]=Math.min(i[0],e,n);i[1]=Math.min(i[1],t,r);i[2]=Math.max(i[2],e,n);i[3]=Math.max(i[3],t,r)}static#e(e,t,n,r,i,s,o,a,c,l){if(c<=0||c>=1)return;const h=1-c,f=c*c,u=f*c,d=h*(h*(h*e+3*c*t)+3*f*n)+u*r,m=h*(h*(h*i+3*c*s)+3*f*o)+u*a;l[0]=Math.min(l[0],d);l[1]=Math.min(l[1],m);l[2]=Math.max(l[2],d);l[3]=Math.max(l[3],m)}static#t(e,t,n,r,i,s,o,a,c,l,h,f){if(Math.abs(c)<1e-12){Math.abs(l)>=1e-12&&this.#e(e,t,n,r,i,s,o,a,-h/l,f);return}const u=l**2-4*h*c;if(u<0)return;const d=Math.sqrt(u),m=2*c;this.#e(e,t,n,r,i,s,o,a,(-l+d)/m,f);this.#e(e,t,n,r,i,s,o,a,(-l-d)/m,f)}static bezierBoundingBox(e,t,n,r,i,s,o,a,c){c[0]=Math.min(c[0],e,o);c[1]=Math.min(c[1],t,a);c[2]=Math.max(c[2],e,o);c[3]=Math.max(c[3],t,a);this.#t(e,n,i,o,t,r,s,a,3*(3*(n-i)-e+o),6*(e-2*n+i),3*(n-e),c);this.#t(e,n,i,o,t,r,s,a,3*(3*(r-s)-t+a),6*(t-2*r+s),3*(r-t),c)}}function MathClamp(e,t,n){return Math.min(Math.max(e,t),n)}"function"!=typeof Promise.try&&(Promise.try=function(e,...t){return new Promise((n=>{n(e(...t))}))});"function"!=typeof Math.sumPrecise&&(Math.sumPrecise=function(e){return e.reduce(((e,t)=>e+t),0)});Symbol("CIRCULAR_REF"),Symbol("EOF");Object.create(null);let i=Object.create(null),s=Object.create(null);class Name{constructor(e){this.name=e}static get(e){return i[e]||=new Name(e)}}const o=function nonSerializableClosure(){return o};class primitives_Dict{constructor(e=null){this._map=new Map;this.xref=e;this.objId=null;this.suppressEncryption=!1;this.__nonSerializable__=o}assignXref(e){this.xref=e}get size(){return this._map.size}get(e,t,n){let r=this._map.get(e);if(void 0===r&&void 0!==t){r=this._map.get(t);void 0===r&&void 0!==n&&(r=this._map.get(n))}return r instanceof primitives_Ref&&this.xref?this.xref.fetch(r,this.suppressEncryption):r}async getAsync(e,t,n){let r=this._map.get(e);if(void 0===r&&void 0!==t){r=this._map.get(t);void 0===r&&void 0!==n&&(r=this._map.get(n))}return r instanceof primitives_Ref&&this.xref?this.xref.fetchAsync(r,this.suppressEncryption):r}getArray(e,t,n){let r=this._map.get(e);if(void 0===r&&void 0!==t){r=this._map.get(t);void 0===r&&void 0!==n&&(r=this._map.get(n))}r instanceof primitives_Ref&&this.xref&&(r=this.xref.fetch(r,this.suppressEncryption));if(Array.isArray(r)){r=r.slice();for(let e=0,t=r.length;e<t;e++)r[e]instanceof primitives_Ref&&this.xref&&(r[e]=this.xref.fetch(r[e],this.suppressEncryption))}return r}getRaw(e){return this._map.get(e)}getKeys(){return[...this._map.keys()]}getRawValues(){return[...this._map.values()]}set(e,t){this._map.set(e,t)}has(e){return this._map.has(e)}*[Symbol.iterator](){for(const[e,t]of this._map)yield[e,t instanceof primitives_Ref&&this.xref?this.xref.fetch(t,this.suppressEncryption):t]}static get empty(){const e=new primitives_Dict(null);e.set=(e,t)=>{unreachable("Should not call `set` on the empty dictionary.")};return shadow(this,"empty",e)}static merge({xref:e,dictArray:t,mergeSubDicts:n=!1}){const r=new primitives_Dict(e),i=new Map;for(const e of t)if(e instanceof primitives_Dict)for(const[t,r]of e._map){let e=i.get(t);if(void 0===e){e=[];i.set(t,e)}else if(!(n&&r instanceof primitives_Dict))continue;e.push(r)}for(const[t,n]of i){if(1===n.length||!(n[0]instanceof primitives_Dict)){r._map.set(t,n[0]);continue}const i=new primitives_Dict(e);for(const e of n)for(const[t,n]of e._map)i._map.has(t)||i._map.set(t,n);i.size>0&&r._map.set(t,i)}i.clear();return r.size>0?r:primitives_Dict.empty}clone(){const e=new primitives_Dict(this.xref);for(const t of this.getKeys())e.set(t,this.getRaw(t));return e}delete(e){delete this._map[e]}}class primitives_Ref{constructor(e,t){this.num=e;this.gen=t}toString(){return 0===this.gen?`${this.num}R`:`${this.num}R${this.gen}`}static fromString(e){const t=s[e];if(t)return t;const n=/^(\d+)R(\d*)$/.exec(e);return n&&"0"!==n[1]?s[e]=new primitives_Ref(parseInt(n[1]),n[2]?parseInt(n[2]):0):null}static get(e,t){const n=0===t?`${e}R`:`${e}R${t}`;return s[n]||=new primitives_Ref(e,t)}}Symbol.iterator;Symbol.iterator;class base_stream_BaseStream{get length(){unreachable("Abstract getter `length` accessed")}get isEmpty(){unreachable("Abstract getter `isEmpty` accessed")}get isDataLoaded(){return shadow(this,"isDataLoaded",!0)}getByte(){unreachable("Abstract method `getByte` called")}getBytes(e){unreachable("Abstract method `getBytes` called")}async getImageData(e,t){return this.getBytes(e,t)}async asyncGetBytes(){unreachable("Abstract method `asyncGetBytes` called")}get isAsync(){return!1}get isAsyncDecoder(){return!1}get canAsyncDecodeImageFromBuffer(){return!1}async getTransferableImage(){return null}peekByte(){const e=this.getByte();-1!==e&&this.pos--;return e}peekBytes(e){const t=this.getBytes(e);this.pos-=t.length;return t}getUint16(){const e=this.getByte(),t=this.getByte();return-1===e||-1===t?-1:(e<<8)+t}getInt32(){return(this.getByte()<<24)+(this.getByte()<<16)+(this.getByte()<<8)+this.getByte()}getByteRange(e,t){unreachable("Abstract method `getByteRange` called")}getString(e){return bytesToString(this.getBytes(e))}skip(e){this.pos+=e||1}reset(){unreachable("Abstract method `reset` called")}moveStart(){unreachable("Abstract method `moveStart` called")}makeSubStream(e,t,n=null){unreachable("Abstract method `makeSubStream` called")}getBaseStreams(){return null}}class MissingDataException extends n{constructor(e,t){super(`Missing data [${e}, ${t})`,"MissingDataException");this.begin=e;this.end=t}}function log2(e){return e>0?Math.ceil(Math.log2(e)):0}function readInt8(e,t){return e[t]<<24>>24}function readUint16(e,t){return e[t]<<8|e[t+1]}function readUint32(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0}const a=[{qe:22017,nmps:1,nlps:1,switchFlag:1},{qe:13313,nmps:2,nlps:6,switchFlag:0},{qe:6145,nmps:3,nlps:9,switchFlag:0},{qe:2753,nmps:4,nlps:12,switchFlag:0},{qe:1313,nmps:5,nlps:29,switchFlag:0},{qe:545,nmps:38,nlps:33,switchFlag:0},{qe:22017,nmps:7,nlps:6,switchFlag:1},{qe:21505,nmps:8,nlps:14,switchFlag:0},{qe:18433,nmps:9,nlps:14,switchFlag:0},{qe:14337,nmps:10,nlps:14,switchFlag:0},{qe:12289,nmps:11,nlps:17,switchFlag:0},{qe:9217,nmps:12,nlps:18,switchFlag:0},{qe:7169,nmps:13,nlps:20,switchFlag:0},{qe:5633,nmps:29,nlps:21,switchFlag:0},{qe:22017,nmps:15,nlps:14,switchFlag:1},{qe:21505,nmps:16,nlps:14,switchFlag:0},{qe:20737,nmps:17,nlps:15,switchFlag:0},{qe:18433,nmps:18,nlps:16,switchFlag:0},{qe:14337,nmps:19,nlps:17,switchFlag:0},{qe:13313,nmps:20,nlps:18,switchFlag:0},{qe:12289,nmps:21,nlps:19,switchFlag:0},{qe:10241,nmps:22,nlps:19,switchFlag:0},{qe:9217,nmps:23,nlps:20,switchFlag:0},{qe:8705,nmps:24,nlps:21,switchFlag:0},{qe:7169,nmps:25,nlps:22,switchFlag:0},{qe:6145,nmps:26,nlps:23,switchFlag:0},{qe:5633,nmps:27,nlps:24,switchFlag:0},{qe:5121,nmps:28,nlps:25,switchFlag:0},{qe:4609,nmps:29,nlps:26,switchFlag:0},{qe:4353,nmps:30,nlps:27,switchFlag:0},{qe:2753,nmps:31,nlps:28,switchFlag:0},{qe:2497,nmps:32,nlps:29,switchFlag:0},{qe:2209,nmps:33,nlps:30,switchFlag:0},{qe:1313,nmps:34,nlps:31,switchFlag:0},{qe:1089,nmps:35,nlps:32,switchFlag:0},{qe:673,nmps:36,nlps:33,switchFlag:0},{qe:545,nmps:37,nlps:34,switchFlag:0},{qe:321,nmps:38,nlps:35,switchFlag:0},{qe:273,nmps:39,nlps:36,switchFlag:0},{qe:133,nmps:40,nlps:37,switchFlag:0},{qe:73,nmps:41,nlps:38,switchFlag:0},{qe:37,nmps:42,nlps:39,switchFlag:0},{qe:21,nmps:43,nlps:40,switchFlag:0},{qe:9,nmps:44,nlps:41,switchFlag:0},{qe:5,nmps:45,nlps:42,switchFlag:0},{qe:1,nmps:45,nlps:43,switchFlag:0},{qe:22017,nmps:46,nlps:46,switchFlag:0}];class ArithmeticDecoder{constructor(e,t,n){this.data=e;this.bp=t;this.dataEnd=n;this.chigh=e[t];this.clow=0;this.byteIn();this.chigh=this.chigh<<7&65535|this.clow>>9&127;this.clow=this.clow<<7&65535;this.ct-=7;this.a=32768}byteIn(){const e=this.data;let t=this.bp;if(255===e[t])if(e[t+1]>143){this.clow+=65280;this.ct=8}else{t++;this.clow+=e[t]<<9;this.ct=7;this.bp=t}else{t++;this.clow+=t<this.dataEnd?e[t]<<8:65280;this.ct=8;this.bp=t}if(this.clow>65535){this.chigh+=this.clow>>16;this.clow&=65535}}readBit(e,t){let n=e[t]>>1,r=1&e[t];const i=a[n],s=i.qe;let o,c=this.a-s;if(this.chigh<s)if(c<s){c=s;o=r;n=i.nmps}else{c=s;o=1^r;1===i.switchFlag&&(r=o);n=i.nlps}else{this.chigh-=s;if(32768&c){this.a=c;return r}if(c<s){o=1^r;1===i.switchFlag&&(r=o);n=i.nlps}else{o=r;n=i.nmps}}do{0===this.ct&&this.byteIn();c<<=1;this.chigh=this.chigh<<1&65535|this.clow>>15&1;this.clow=this.clow<<1&65535;this.ct--}while(!(32768&c));this.a=c;e[t]=n<<1|r;return o}}const c=-1,l=[[-1,-1],[-1,-1],[7,8],[7,7],[6,6],[6,6],[6,5],[6,5],[4,0],[4,0],[4,0],[4,0],[4,0],[4,0],[4,0],[4,0],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2]],h=[[-1,-1],[12,-2],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[11,1792],[11,1792],[12,1984],[12,2048],[12,2112],[12,2176],[12,2240],[12,2304],[11,1856],[11,1856],[11,1920],[11,1920],[12,2368],[12,2432],[12,2496],[12,2560]],f=[[-1,-1],[-1,-1],[-1,-1],[-1,-1],[8,29],[8,29],[8,30],[8,30],[8,45],[8,45],[8,46],[8,46],[7,22],[7,22],[7,22],[7,22],[7,23],[7,23],[7,23],[7,23],[8,47],[8,47],[8,48],[8,48],[6,13],[6,13],[6,13],[6,13],[6,13],[6,13],[6,13],[6,13],[7,20],[7,20],[7,20],[7,20],[8,33],[8,33],[8,34],[8,34],[8,35],[8,35],[8,36],[8,36],[8,37],[8,37],[8,38],[8,38],[7,19],[7,19],[7,19],[7,19],[8,31],[8,31],[8,32],[8,32],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,12],[6,12],[6,12],[6,12],[6,12],[6,12],[6,12],[6,12],[8,53],[8,53],[8,54],[8,54],[7,26],[7,26],[7,26],[7,26],[8,39],[8,39],[8,40],[8,40],[8,41],[8,41],[8,42],[8,42],[8,43],[8,43],[8,44],[8,44],[7,21],[7,21],[7,21],[7,21],[7,28],[7,28],[7,28],[7,28],[8,61],[8,61],[8,62],[8,62],[8,63],[8,63],[8,0],[8,0],[8,320],[8,320],[8,384],[8,384],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[7,27],[7,27],[7,27],[7,27],[8,59],[8,59],[8,60],[8,60],[9,1472],[9,1536],[9,1600],[9,1728],[7,18],[7,18],[7,18],[7,18],[7,24],[7,24],[7,24],[7,24],[8,49],[8,49],[8,50],[8,50],[8,51],[8,51],[8,52],[8,52],[7,25],[7,25],[7,25],[7,25],[8,55],[8,55],[8,56],[8,56],[8,57],[8,57],[8,58],[8,58],[6,192],[6,192],[6,192],[6,192],[6,192],[6,192],[6,192],[6,192],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[8,448],[8,448],[8,512],[8,512],[9,704],[9,768],[8,640],[8,640],[8,576],[8,576],[9,832],[9,896],[9,960],[9,1024],[9,1088],[9,1152],[9,1216],[9,1280],[9,1344],[9,1408],[7,256],[7,256],[7,256],[7,256],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[6,16],[6,16],[6,16],[6,16],[6,16],[6,16],[6,16],[6,16],[6,17],[6,17],[6,17],[6,17],[6,17],[6,17],[6,17],[6,17],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[6,14],[6,14],[6,14],[6,14],[6,14],[6,14],[6,14],[6,14],[6,15],[6,15],[6,15],[6,15],[6,15],[6,15],[6,15],[6,15],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7]],u=[[-1,-1],[-1,-1],[12,-2],[12,-2],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[11,1792],[11,1792],[11,1792],[11,1792],[12,1984],[12,1984],[12,2048],[12,2048],[12,2112],[12,2112],[12,2176],[12,2176],[12,2240],[12,2240],[12,2304],[12,2304],[11,1856],[11,1856],[11,1856],[11,1856],[11,1920],[11,1920],[11,1920],[11,1920],[12,2368],[12,2368],[12,2432],[12,2432],[12,2496],[12,2496],[12,2560],[12,2560],[10,18],[10,18],[10,18],[10,18],[10,18],[10,18],[10,18],[10,18],[12,52],[12,52],[13,640],[13,704],[13,768],[13,832],[12,55],[12,55],[12,56],[12,56],[13,1280],[13,1344],[13,1408],[13,1472],[12,59],[12,59],[12,60],[12,60],[13,1536],[13,1600],[11,24],[11,24],[11,24],[11,24],[11,25],[11,25],[11,25],[11,25],[13,1664],[13,1728],[12,320],[12,320],[12,384],[12,384],[12,448],[12,448],[13,512],[13,576],[12,53],[12,53],[12,54],[12,54],[13,896],[13,960],[13,1024],[13,1088],[13,1152],[13,1216],[10,64],[10,64],[10,64],[10,64],[10,64],[10,64],[10,64],[10,64]],d=[[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[11,23],[11,23],[12,50],[12,51],[12,44],[12,45],[12,46],[12,47],[12,57],[12,58],[12,61],[12,256],[10,16],[10,16],[10,16],[10,16],[10,17],[10,17],[10,17],[10,17],[12,48],[12,49],[12,62],[12,63],[12,30],[12,31],[12,32],[12,33],[12,40],[12,41],[11,22],[11,22],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[9,15],[9,15],[9,15],[9,15],[9,15],[9,15],[9,15],[9,15],[12,128],[12,192],[12,26],[12,27],[12,28],[12,29],[11,19],[11,19],[11,20],[11,20],[12,34],[12,35],[12,36],[12,37],[12,38],[12,39],[11,21],[11,21],[12,42],[12,43],[10,0],[10,0],[10,0],[10,0],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12]],m=[[-1,-1],[-1,-1],[-1,-1],[-1,-1],[6,9],[6,8],[5,7],[5,7],[4,6],[4,6],[4,6],[4,6],[4,5],[4,5],[4,5],[4,5],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2]];class CCITTFaxDecoder{constructor(e,t={}){if("function"!=typeof e?.next)throw new Error('CCITTFaxDecoder - invalid "source" parameter.');this.source=e;this.eof=!1;this.encoding=t.K||0;this.eoline=t.EndOfLine||!1;this.byteAlign=t.EncodedByteAlign||!1;this.columns=t.Columns||1728;this.rows=t.Rows||0;this.eoblock=t.EndOfBlock??!0;this.black=t.BlackIs1||!1;this.codingLine=new Uint32Array(this.columns+1);this.refLine=new Uint32Array(this.columns+2);this.codingLine[0]=this.columns;this.codingPos=0;this.row=0;this.nextLine2D=this.encoding<0;this.inputBits=0;this.inputBuf=0;this.outputBits=0;this.rowsDone=!1;let n;for(;0===(n=this._lookBits(12));)this._eatBits(1);1===n&&this._eatBits(12);if(this.encoding>0){this.nextLine2D=!this._lookBits(1);this._eatBits(1)}}readNextChar(){if(this.eof)return-1;const e=this.refLine,t=this.codingLine,n=this.columns;let r,i,s,o,a;if(0===this.outputBits){this.rowsDone&&(this.eof=!0);if(this.eof)return-1;this.err=!1;let s,a,l;if(this.nextLine2D){for(o=0;t[o]<n;++o)e[o]=t[o];e[o++]=n;e[o]=n;t[0]=0;this.codingPos=0;r=0;i=0;for(;t[this.codingPos]<n;){s=this._getTwoDimCode();switch(s){case 0:this._addPixels(e[r+1],i);e[r+1]<n&&(r+=2);break;case 1:s=a=0;if(i){do{s+=l=this._getBlackCode()}while(l>=64);do{a+=l=this._getWhiteCode()}while(l>=64)}else{do{s+=l=this._getWhiteCode()}while(l>=64);do{a+=l=this._getBlackCode()}while(l>=64)}this._addPixels(t[this.codingPos]+s,i);t[this.codingPos]<n&&this._addPixels(t[this.codingPos]+a,1^i);for(;e[r]<=t[this.codingPos]&&e[r]<n;)r+=2;break;case 7:this._addPixels(e[r]+3,i);i^=1;if(t[this.codingPos]<n){++r;for(;e[r]<=t[this.codingPos]&&e[r]<n;)r+=2}break;case 5:this._addPixels(e[r]+2,i);i^=1;if(t[this.codingPos]<n){++r;for(;e[r]<=t[this.codingPos]&&e[r]<n;)r+=2}break;case 3:this._addPixels(e[r]+1,i);i^=1;if(t[this.codingPos]<n){++r;for(;e[r]<=t[this.codingPos]&&e[r]<n;)r+=2}break;case 2:this._addPixels(e[r],i);i^=1;if(t[this.codingPos]<n){++r;for(;e[r]<=t[this.codingPos]&&e[r]<n;)r+=2}break;case 8:this._addPixelsNeg(e[r]-3,i);i^=1;if(t[this.codingPos]<n){r>0?--r:++r;for(;e[r]<=t[this.codingPos]&&e[r]<n;)r+=2}break;case 6:this._addPixelsNeg(e[r]-2,i);i^=1;if(t[this.codingPos]<n){r>0?--r:++r;for(;e[r]<=t[this.codingPos]&&e[r]<n;)r+=2}break;case 4:this._addPixelsNeg(e[r]-1,i);i^=1;if(t[this.codingPos]<n){r>0?--r:++r;for(;e[r]<=t[this.codingPos]&&e[r]<n;)r+=2}break;case c:this._addPixels(n,0);this.eof=!0;break;default:info("bad 2d code");this._addPixels(n,0);this.err=!0}}}else{t[0]=0;this.codingPos=0;i=0;for(;t[this.codingPos]<n;){s=0;if(i)do{s+=l=this._getBlackCode()}while(l>=64);else do{s+=l=this._getWhiteCode()}while(l>=64);this._addPixels(t[this.codingPos]+s,i);i^=1}}let h=!1;this.byteAlign&&(this.inputBits&=-8);if(this.eoblock||this.row!==this.rows-1){s=this._lookBits(12);if(this.eoline)for(;s!==c&&1!==s;){this._eatBits(1);s=this._lookBits(12)}else for(;0===s;){this._eatBits(1);s=this._lookBits(12)}if(1===s){this._eatBits(12);h=!0}else s===c&&(this.eof=!0)}else this.rowsDone=!0;if(!this.eof&&this.encoding>0&&!this.rowsDone){this.nextLine2D=!this._lookBits(1);this._eatBits(1)}if(this.eoblock&&h&&this.byteAlign){s=this._lookBits(12);if(1===s){this._eatBits(12);if(this.encoding>0){this._lookBits(1);this._eatBits(1)}if(this.encoding>=0)for(o=0;o<4;++o){s=this._lookBits(12);1!==s&&info("bad rtc code: "+s);this._eatBits(12);if(this.encoding>0){this._lookBits(1);this._eatBits(1)}}this.eof=!0}}else if(this.err&&this.eoline){for(;;){s=this._lookBits(13);if(s===c){this.eof=!0;return-1}if(s>>1==1)break;this._eatBits(1)}this._eatBits(12);if(this.encoding>0){this._eatBits(1);this.nextLine2D=!(1&s)}}this.outputBits=t[0]>0?t[this.codingPos=0]:t[this.codingPos=1];this.row++}if(this.outputBits>=8){a=1&this.codingPos?0:255;this.outputBits-=8;if(0===this.outputBits&&t[this.codingPos]<n){this.codingPos++;this.outputBits=t[this.codingPos]-t[this.codingPos-1]}}else{s=8;a=0;do{if("number"!=typeof this.outputBits)throw new FormatError('Invalid /CCITTFaxDecode data, "outputBits" must be a number.');if(this.outputBits>s){a<<=s;1&this.codingPos||(a|=255>>8-s);this.outputBits-=s;s=0}else{a<<=this.outputBits;1&this.codingPos||(a|=255>>8-this.outputBits);s-=this.outputBits;this.outputBits=0;if(t[this.codingPos]<n){this.codingPos++;this.outputBits=t[this.codingPos]-t[this.codingPos-1]}else if(s>0){a<<=s;s=0}}}while(s)}this.black&&(a^=255);return a}_addPixels(e,t){const n=this.codingLine;let r=this.codingPos;if(e>n[r]){if(e>this.columns){info("row is wrong length");this.err=!0;e=this.columns}1&r^t&&++r;n[r]=e}this.codingPos=r}_addPixelsNeg(e,t){const n=this.codingLine;let r=this.codingPos;if(e>n[r]){if(e>this.columns){info("row is wrong length");this.err=!0;e=this.columns}1&r^t&&++r;n[r]=e}else if(e<n[r]){if(e<0){info("invalid code");this.err=!0;e=0}for(;r>0&&e<n[r-1];)--r;n[r]=e}this.codingPos=r}_findTableCode(e,t,n,r){const i=r||0;for(let r=e;r<=t;++r){let e=this._lookBits(r);if(e===c)return[!0,1,!1];r<t&&(e<<=t-r);if(!i||e>=i){const t=n[e-i];if(t[0]===r){this._eatBits(r);return[!0,t[1],!0]}}}return[!1,0,!1]}_getTwoDimCode(){let e,t=0;if(this.eoblock){t=this._lookBits(7);e=l[t];if(e?.[0]>0){this._eatBits(e[0]);return e[1]}}else{const e=this._findTableCode(1,7,l);if(e[0]&&e[2])return e[1]}info("Bad two dim code");return c}_getWhiteCode(){let e,t=0;if(this.eoblock){t=this._lookBits(12);if(t===c)return 1;e=t>>5?f[t>>3]:h[t];if(e[0]>0){this._eatBits(e[0]);return e[1]}}else{let e=this._findTableCode(1,9,f);if(e[0])return e[1];e=this._findTableCode(11,12,h);if(e[0])return e[1]}info("bad white code");this._eatBits(1);return 1}_getBlackCode(){let e,t;if(this.eoblock){e=this._lookBits(13);if(e===c)return 1;t=e>>7?!(e>>9)&&e>>7?d[(e>>1)-64]:m[e>>7]:u[e];if(t[0]>0){this._eatBits(t[0]);return t[1]}}else{let e=this._findTableCode(2,6,m);if(e[0])return e[1];e=this._findTableCode(7,12,d,64);if(e[0])return e[1];e=this._findTableCode(10,13,u);if(e[0])return e[1]}info("bad black code");this._eatBits(1);return 1}_lookBits(e){let t;for(;this.inputBits<e;){if(-1===(t=this.source.next()))return 0===this.inputBits?c:this.inputBuf<<e-this.inputBits&65535>>16-e;this.inputBuf=this.inputBuf<<8|t;this.inputBits+=8}return this.inputBuf>>this.inputBits-e&65535>>16-e}_eatBits(e){(this.inputBits-=e)<0&&(this.inputBits=0)}}class Jbig2Error extends n{constructor(e){super(e,"Jbig2Error")}}class ContextCache{getContexts(e){return e in this?this[e]:this[e]=new Int8Array(65536)}}class DecodingContext{constructor(e,t,n){this.data=e;this.start=t;this.end=n}get decoder(){return shadow(this,"decoder",new ArithmeticDecoder(this.data,this.start,this.end))}get contextCache(){return shadow(this,"contextCache",new ContextCache)}}function decodeInteger(e,t,n){const r=e.getContexts(t);let i=1;function readBits(e){let t=0;for(let s=0;s<e;s++){const e=n.readBit(r,i);i=i<256?i<<1|e:511&(i<<1|e)|256;t=t<<1|e}return t>>>0}const s=readBits(1),o=readBits(1)?readBits(1)?readBits(1)?readBits(1)?readBits(1)?readBits(32)+4436:readBits(12)+340:readBits(8)+84:readBits(6)+20:readBits(4)+4:readBits(2);let a;0===s?a=o:o>0&&(a=-o);return a>=-2147483648&&a<=2147483647?a:null}function decodeIAID(e,t,n){const r=e.getContexts("IAID");let i=1;for(let e=0;e<n;e++){i=i<<1|t.readBit(r,i)}return n<31?i&(1<<n)-1:2147483647&i}const g=["SymbolDictionary",null,null,null,"IntermediateTextRegion",null,"ImmediateTextRegion","ImmediateLosslessTextRegion",null,null,null,null,null,null,null,null,"PatternDictionary",null,null,null,"IntermediateHalftoneRegion",null,"ImmediateHalftoneRegion","ImmediateLosslessHalftoneRegion",null,null,null,null,null,null,null,null,null,null,null,null,"IntermediateGenericRegion",null,"ImmediateGenericRegion","ImmediateLosslessGenericRegion","IntermediateGenericRefinementRegion",null,"ImmediateGenericRefinementRegion","ImmediateLosslessGenericRefinementRegion",null,null,null,null,"PageInformation","EndOfPage","EndOfStripe","EndOfFile","Profiles","Tables",null,null,null,null,null,null,null,null,"Extension"],p=[[{x:-1,y:-2},{x:0,y:-2},{x:1,y:-2},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:2,y:-1},{x:-4,y:0},{x:-3,y:0},{x:-2,y:0},{x:-1,y:0}],[{x:-1,y:-2},{x:0,y:-2},{x:1,y:-2},{x:2,y:-2},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:2,y:-1},{x:-3,y:0},{x:-2,y:0},{x:-1,y:0}],[{x:-1,y:-2},{x:0,y:-2},{x:1,y:-2},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:-2,y:0},{x:-1,y:0}],[{x:-3,y:-1},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:-4,y:0},{x:-3,y:0},{x:-2,y:0},{x:-1,y:0}]],b=[{coding:[{x:0,y:-1},{x:1,y:-1},{x:-1,y:0}],reference:[{x:0,y:-1},{x:1,y:-1},{x:-1,y:0},{x:0,y:0},{x:1,y:0},{x:-1,y:1},{x:0,y:1},{x:1,y:1}]},{coding:[{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:-1,y:0}],reference:[{x:0,y:-1},{x:-1,y:0},{x:0,y:0},{x:1,y:0},{x:0,y:1},{x:1,y:1}]}],w=[39717,1941,229,405],y=[32,8];function decodeBitmap(e,t,n,r,i,s,o,a){if(e){return decodeMMRBitmap(new Reader(a.data,a.start,a.end),t,n,!1)}if(0===r&&!s&&!i&&4===o.length&&3===o[0].x&&-1===o[0].y&&-3===o[1].x&&-1===o[1].y&&2===o[2].x&&-2===o[2].y&&-2===o[3].x&&-2===o[3].y)return function decodeBitmapTemplate0(e,t,n){const r=n.decoder,i=n.contextCache.getContexts("GB"),s=[];let o,a,c,l,h,f,u;for(a=0;a<t;a++){h=s[a]=new Uint8Array(e);f=a<1?h:s[a-1];u=a<2?h:s[a-2];o=u[0]<<13|u[1]<<12|u[2]<<11|f[0]<<7|f[1]<<6|f[2]<<5|f[3]<<4;for(c=0;c<e;c++){h[c]=l=r.readBit(i,o);o=(31735&o)<<1|(c+3<e?u[c+3]<<11:0)|(c+4<e?f[c+4]<<4:0)|l}}return s}(t,n,a);const c=!!s,l=p[r].concat(o);l.sort(((e,t)=>e.y-t.y||e.x-t.x));const h=l.length,f=new Int8Array(h),u=new Int8Array(h),d=[];let m,g,b=0,y=0,C=0,x=0;for(g=0;g<h;g++){f[g]=l[g].x;u[g]=l[g].y;y=Math.min(y,l[g].x);C=Math.max(C,l[g].x);x=Math.min(x,l[g].y);g<h-1&&l[g].y===l[g+1].y&&l[g].x===l[g+1].x-1?b|=1<<h-1-g:d.push(g)}const _=d.length,B=new Int8Array(_),R=new Int8Array(_),S=new Uint16Array(_);for(m=0;m<_;m++){g=d[m];B[m]=l[g].x;R[m]=l[g].y;S[m]=1<<h-1-g}const A=-y,k=-x,I=t-C,v=w[r];let D=new Uint8Array(t);const M=[],T=a.decoder,P=a.contextCache.getContexts("GB");let E,U,L,F,O,G=0,W=0;for(let e=0;e<n;e++){if(i){G^=T.readBit(P,v);if(G){M.push(D);continue}}D=new Uint8Array(D);M.push(D);for(E=0;E<t;E++){if(c&&s[e][E]){D[E]=0;continue}if(E>=A&&E<I&&e>=k){W=W<<1&b;for(g=0;g<_;g++){U=e+R[g];L=E+B[g];F=M[U][L];if(F){F=S[g];W|=F}}}else{W=0;O=h-1;for(g=0;g<h;g++,O--){L=E+f[g];if(L>=0&&L<t){U=e+u[g];if(U>=0){F=M[U][L];F&&(W|=F<<O)}}}}const n=T.readBit(P,W);D[E]=n}}return M}function decodeRefinement(e,t,n,r,i,s,o,a,c){let l=b[n].coding;0===n&&(l=l.concat([a[0]]));const h=l.length,f=new Int32Array(h),u=new Int32Array(h);let d;for(d=0;d<h;d++){f[d]=l[d].x;u[d]=l[d].y}let m=b[n].reference;0===n&&(m=m.concat([a[1]]));const g=m.length,p=new Int32Array(g),w=new Int32Array(g);for(d=0;d<g;d++){p[d]=m[d].x;w[d]=m[d].y}const C=r[0].length,x=r.length,_=y[n],B=[],R=c.decoder,S=c.contextCache.getContexts("GR");let A=0;for(let n=0;n<t;n++){if(o){A^=R.readBit(S,_);if(A)throw new Jbig2Error("prediction is not supported")}const t=new Uint8Array(e);B.push(t);for(let o=0;o<e;o++){let a,c,l=0;for(d=0;d<h;d++){a=n+u[d];c=o+f[d];a<0||c<0||c>=e?l<<=1:l=l<<1|B[a][c]}for(d=0;d<g;d++){a=n+w[d]-s;c=o+p[d]-i;a<0||a>=x||c<0||c>=C?l<<=1:l=l<<1|r[a][c]}const m=R.readBit(S,l);t[o]=m}}return B}function decodeTextRegion(e,t,n,r,i,s,o,a,c,l,h,f,u,d,m,g,p,b,w){if(e&&t)throw new Jbig2Error("refinement with Huffman is not supported");const y=[];let C,x;for(C=0;C<r;C++){x=new Uint8Array(n);i&&x.fill(i);y.push(x)}const _=p.decoder,B=p.contextCache;let R=e?-d.tableDeltaT.decode(w):-decodeInteger(B,"IADT",_),S=0;C=0;for(;C<s;){R+=e?d.tableDeltaT.decode(w):decodeInteger(B,"IADT",_);S+=e?d.tableFirstS.decode(w):decodeInteger(B,"IAFS",_);let r=S;for(;;){let i=0;o>1&&(i=e?w.readBits(b):decodeInteger(B,"IAIT",_));const s=o*R+i,S=e?d.symbolIDTable.decode(w):decodeIAID(B,_,c),A=t&&(e?w.readBit():decodeInteger(B,"IARI",_));let k=a[S],I=k[0].length,v=k.length;if(A){const e=decodeInteger(B,"IARDW",_),t=decodeInteger(B,"IARDH",_);I+=e;v+=t;k=decodeRefinement(I,v,m,k,(e>>1)+decodeInteger(B,"IARDX",_),(t>>1)+decodeInteger(B,"IARDY",_),!1,g,p)}let D=0;l?1&f?D=v-1:r+=v-1:f>1?r+=I-1:D=I-1;const M=s-(1&f?0:v-1),T=r-(2&f?I-1:0);let P,E,U;if(l)for(P=0;P<v;P++){x=y[T+P];if(!x)continue;U=k[P];const e=Math.min(n-M,I);switch(u){case 0:for(E=0;E<e;E++)x[M+E]|=U[E];break;case 2:for(E=0;E<e;E++)x[M+E]^=U[E];break;default:throw new Jbig2Error(`operator ${u} is not supported`)}}else for(E=0;E<v;E++){x=y[M+E];if(x){U=k[E];switch(u){case 0:for(P=0;P<I;P++)x[T+P]|=U[P];break;case 2:for(P=0;P<I;P++)x[T+P]^=U[P];break;default:throw new Jbig2Error(`operator ${u} is not supported`)}}}C++;const L=e?d.tableDeltaS.decode(w):decodeInteger(B,"IADS",_);if(null===L)break;r+=D+L+h}}return y}function readSegmentHeader(e,t){const n={};n.number=readUint32(e,t);const r=e[t+4],i=63&r;if(!g[i])throw new Jbig2Error("invalid segment type: "+i);n.type=i;n.typeName=g[i];n.deferredNonRetain=!!(128&r);const s=!!(64&r),o=e[t+5];let a=o>>5&7;const c=[31&o];let l=t+6;if(7===o){a=536870911&readUint32(e,l-1);l+=3;let t=a+7>>3;c[0]=e[l++];for(;--t>0;)c.push(e[l++])}else if(5===o||6===o)throw new Jbig2Error("invalid referred-to flags");n.retainBits=c;let h=4;n.number<=256?h=1:n.number<=65536&&(h=2);const f=[];let u,d;for(u=0;u<a;u++){let t;t=1===h?e[l]:2===h?readUint16(e,l):readUint32(e,l);f.push(t);l+=h}n.referredTo=f;if(s){n.pageAssociation=readUint32(e,l);l+=4}else n.pageAssociation=e[l++];n.length=readUint32(e,l);l+=4;if(4294967295===n.length){if(38!==i)throw new Jbig2Error("invalid unknown segment length");{const t=readRegionSegmentInformation(e,l),r=!!(1&e[l+C]),i=6,s=new Uint8Array(i);if(!r){s[0]=255;s[1]=172}s[2]=t.height>>>24&255;s[3]=t.height>>16&255;s[4]=t.height>>8&255;s[5]=255&t.height;for(u=l,d=e.length;u<d;u++){let t=0;for(;t<i&&s[t]===e[u+t];)t++;if(t===i){n.length=u+i;break}}if(4294967295===n.length)throw new Jbig2Error("segment end was not found")}}n.headerEnd=l;return n}function readSegments(e,t,n,r){const i=[];let s=n;for(;s<r;){const n=readSegmentHeader(t,s);s=n.headerEnd;const r={header:n,data:t};if(!e.randomAccess){r.start=s;s+=n.length;r.end=s}i.push(r);if(51===n.type)break}if(e.randomAccess)for(let e=0,t=i.length;e<t;e++){i[e].start=s;s+=i[e].header.length;i[e].end=s}return i}function readRegionSegmentInformation(e,t){return{width:readUint32(e,t),height:readUint32(e,t+4),x:readUint32(e,t+8),y:readUint32(e,t+12),combinationOperator:7&e[t+16]}}const C=17;function processSegment(e,t){const n=e.header,r=e.data,i=e.end;let s,o,a,c,l=e.start;switch(n.type){case 0:const e={},t=readUint16(r,l);e.huffman=!!(1&t);e.refinement=!!(2&t);e.huffmanDHSelector=t>>2&3;e.huffmanDWSelector=t>>4&3;e.bitmapSizeSelector=t>>6&1;e.aggregationInstancesSelector=t>>7&1;e.bitmapCodingContextUsed=!!(256&t);e.bitmapCodingContextRetained=!!(512&t);e.template=t>>10&3;e.refinementTemplate=t>>12&1;l+=2;if(!e.huffman){c=0===e.template?4:1;o=[];for(a=0;a<c;a++){o.push({x:readInt8(r,l),y:readInt8(r,l+1)});l+=2}e.at=o}if(e.refinement&&!e.refinementTemplate){o=[];for(a=0;a<2;a++){o.push({x:readInt8(r,l),y:readInt8(r,l+1)});l+=2}e.refinementAt=o}e.numberOfExportedSymbols=readUint32(r,l);l+=4;e.numberOfNewSymbols=readUint32(r,l);l+=4;s=[e,n.number,n.referredTo,r,l,i];break;case 6:case 7:const h={};h.info=readRegionSegmentInformation(r,l);l+=C;const f=readUint16(r,l);l+=2;h.huffman=!!(1&f);h.refinement=!!(2&f);h.logStripSize=f>>2&3;h.stripSize=1<<h.logStripSize;h.referenceCorner=f>>4&3;h.transposed=!!(64&f);h.combinationOperator=f>>7&3;h.defaultPixelValue=f>>9&1;h.dsOffset=f<<17>>27;h.refinementTemplate=f>>15&1;if(h.huffman){const e=readUint16(r,l);l+=2;h.huffmanFS=3&e;h.huffmanDS=e>>2&3;h.huffmanDT=e>>4&3;h.huffmanRefinementDW=e>>6&3;h.huffmanRefinementDH=e>>8&3;h.huffmanRefinementDX=e>>10&3;h.huffmanRefinementDY=e>>12&3;h.huffmanRefinementSizeSelector=!!(16384&e)}if(h.refinement&&!h.refinementTemplate){o=[];for(a=0;a<2;a++){o.push({x:readInt8(r,l),y:readInt8(r,l+1)});l+=2}h.refinementAt=o}h.numberOfSymbolInstances=readUint32(r,l);l+=4;s=[h,n.referredTo,r,l,i];break;case 16:const u={},d=r[l++];u.mmr=!!(1&d);u.template=d>>1&3;u.patternWidth=r[l++];u.patternHeight=r[l++];u.maxPatternIndex=readUint32(r,l);l+=4;s=[u,n.number,r,l,i];break;case 22:case 23:const m={};m.info=readRegionSegmentInformation(r,l);l+=C;const g=r[l++];m.mmr=!!(1&g);m.template=g>>1&3;m.enableSkip=!!(8&g);m.combinationOperator=g>>4&7;m.defaultPixelValue=g>>7&1;m.gridWidth=readUint32(r,l);l+=4;m.gridHeight=readUint32(r,l);l+=4;m.gridOffsetX=4294967295&readUint32(r,l);l+=4;m.gridOffsetY=4294967295&readUint32(r,l);l+=4;m.gridVectorX=readUint16(r,l);l+=2;m.gridVectorY=readUint16(r,l);l+=2;s=[m,n.referredTo,r,l,i];break;case 38:case 39:const p={};p.info=readRegionSegmentInformation(r,l);l+=C;const b=r[l++];p.mmr=!!(1&b);p.template=b>>1&3;p.prediction=!!(8&b);if(!p.mmr){c=0===p.template?4:1;o=[];for(a=0;a<c;a++){o.push({x:readInt8(r,l),y:readInt8(r,l+1)});l+=2}p.at=o}s=[p,r,l,i];break;case 48:const w={width:readUint32(r,l),height:readUint32(r,l+4),resolutionX:readUint32(r,l+8),resolutionY:readUint32(r,l+12)};4294967295===w.height&&delete w.height;const y=r[l+16];readUint16(r,l+17);w.lossless=!!(1&y);w.refinement=!!(2&y);w.defaultPixelValue=y>>2&1;w.combinationOperator=y>>3&3;w.requiresBuffer=!!(32&y);w.combinationOperatorOverride=!!(64&y);s=[w];break;case 49:case 50:case 51:case 62:break;case 53:s=[n.number,r,l,i];break;default:throw new Jbig2Error(`segment type ${n.typeName}(${n.type}) is not implemented`)}const h="on"+n.typeName;h in t&&t[h].apply(t,s)}function processSegments(e,t){for(let n=0,r=e.length;n<r;n++)processSegment(e[n],t)}class SimpleSegmentVisitor{onPageInformation(e){this.currentPageInfo=e;const t=e.width+7>>3,n=new Uint8ClampedArray(t*e.height);e.defaultPixelValue&&n.fill(255);this.buffer=n}drawBitmap(e,t){const n=this.currentPageInfo,r=e.width,i=e.height,s=n.width+7>>3,o=n.combinationOperatorOverride?e.combinationOperator:n.combinationOperator,a=this.buffer,c=128>>(7&e.x);let l,h,f,u,d=e.y*s+(e.x>>3);switch(o){case 0:for(l=0;l<i;l++){f=c;u=d;for(h=0;h<r;h++){t[l][h]&&(a[u]|=f);f>>=1;if(!f){f=128;u++}}d+=s}break;case 2:for(l=0;l<i;l++){f=c;u=d;for(h=0;h<r;h++){t[l][h]&&(a[u]^=f);f>>=1;if(!f){f=128;u++}}d+=s}break;default:throw new Jbig2Error(`operator ${o} is not supported`)}}onImmediateGenericRegion(e,t,n,r){const i=e.info,s=new DecodingContext(t,n,r),o=decodeBitmap(e.mmr,i.width,i.height,e.template,e.prediction,null,e.at,s);this.drawBitmap(i,o)}onImmediateLosslessGenericRegion(){this.onImmediateGenericRegion(...arguments)}onSymbolDictionary(e,t,n,r,i,s){let o,a;if(e.huffman){o=function getSymbolDictionaryHuffmanTables(e,t,n){let r,i,s,o,a=0;switch(e.huffmanDHSelector){case 0:case 1:r=getStandardTable(e.huffmanDHSelector+4);break;case 3:r=getCustomHuffmanTable(a,t,n);a++;break;default:throw new Jbig2Error("invalid Huffman DH selector")}switch(e.huffmanDWSelector){case 0:case 1:i=getStandardTable(e.huffmanDWSelector+2);break;case 3:i=getCustomHuffmanTable(a,t,n);a++;break;default:throw new Jbig2Error("invalid Huffman DW selector")}if(e.bitmapSizeSelector){s=getCustomHuffmanTable(a,t,n);a++}else s=getStandardTable(1);o=e.aggregationInstancesSelector?getCustomHuffmanTable(a,t,n):getStandardTable(1);return{tableDeltaHeight:r,tableDeltaWidth:i,tableBitmapSize:s,tableAggregateInstances:o}}(e,n,this.customTables);a=new Reader(r,i,s)}let c=this.symbols;c||(this.symbols=c={});const l=[];for(const e of n){const t=c[e];t&&l.push(...t)}const h=new DecodingContext(r,i,s);c[t]=function decodeSymbolDictionary(e,t,n,r,i,s,o,a,c,l,h,f){if(e&&t)throw new Jbig2Error("symbol refinement with Huffman is not supported");const u=[];let d=0,m=log2(n.length+r);const g=h.decoder,p=h.contextCache;let b,w;if(e){b=getStandardTable(1);w=[];m=Math.max(m,1)}for(;u.length<r;){d+=e?s.tableDeltaHeight.decode(f):decodeInteger(p,"IADH",g);let r=0,i=0;const b=e?w.length:0;for(;;){const b=e?s.tableDeltaWidth.decode(f):decodeInteger(p,"IADW",g);if(null===b)break;r+=b;i+=r;let y;if(t){const i=decodeInteger(p,"IAAI",g);if(i>1)y=decodeTextRegion(e,t,r,d,0,i,1,n.concat(u),m,0,0,1,0,s,c,l,h,0,f);else{const e=decodeIAID(p,g,m),t=decodeInteger(p,"IARDX",g),i=decodeInteger(p,"IARDY",g);y=decodeRefinement(r,d,c,e<n.length?n[e]:u[e-n.length],t,i,!1,l,h)}u.push(y)}else if(e)w.push(r);else{y=decodeBitmap(!1,r,d,o,!1,null,a,h);u.push(y)}}if(e&&!t){const e=s.tableBitmapSize.decode(f);f.byteAlign();let t;if(0===e)t=readUncompressedBitmap(f,i,d);else{const n=f.end,r=f.position+e;f.end=r;t=decodeMMRBitmap(f,i,d,!1);f.end=n;f.position=r}const n=w.length;if(b===n-1)u.push(t);else{let e,r,i,s,o,a=0;for(e=b;e<n;e++){s=w[e];i=a+s;o=[];for(r=0;r<d;r++)o.push(t[r].subarray(a,i));u.push(o);a=i}}}}const y=[],C=[];let x,_,B=!1;const R=n.length+r;for(;C.length<R;){let t=e?b.decode(f):decodeInteger(p,"IAEX",g);for(;t--;)C.push(B);B=!B}for(x=0,_=n.length;x<_;x++)C[x]&&y.push(n[x]);for(let e=0;e<r;x++,e++)C[x]&&y.push(u[e]);return y}(e.huffman,e.refinement,l,e.numberOfNewSymbols,e.numberOfExportedSymbols,o,e.template,e.at,e.refinementTemplate,e.refinementAt,h,a)}onImmediateTextRegion(e,t,n,r,i){const s=e.info;let o,a;const c=this.symbols,l=[];for(const e of t){const t=c[e];t&&l.push(...t)}const h=log2(l.length);if(e.huffman){a=new Reader(n,r,i);o=function getTextRegionHuffmanTables(e,t,n,r,i){const s=[];for(let e=0;e<=34;e++){const t=i.readBits(4);s.push(new HuffmanLine([e,t,0,0]))}const o=new HuffmanTable(s,!1);s.length=0;for(let e=0;e<r;){const t=o.decode(i);if(t>=32){let n,r,o;switch(t){case 32:if(0===e)throw new Jbig2Error("no previous value in symbol ID table");r=i.readBits(2)+3;n=s[e-1].prefixLength;break;case 33:r=i.readBits(3)+3;n=0;break;case 34:r=i.readBits(7)+11;n=0;break;default:throw new Jbig2Error("invalid code length in symbol ID table")}for(o=0;o<r;o++){s.push(new HuffmanLine([e,n,0,0]));e++}}else{s.push(new HuffmanLine([e,t,0,0]));e++}}i.byteAlign();const a=new HuffmanTable(s,!1);let c,l,h,f=0;switch(e.huffmanFS){case 0:case 1:c=getStandardTable(e.huffmanFS+6);break;case 3:c=getCustomHuffmanTable(f,t,n);f++;break;default:throw new Jbig2Error("invalid Huffman FS selector")}switch(e.huffmanDS){case 0:case 1:case 2:l=getStandardTable(e.huffmanDS+8);break;case 3:l=getCustomHuffmanTable(f,t,n);f++;break;default:throw new Jbig2Error("invalid Huffman DS selector")}switch(e.huffmanDT){case 0:case 1:case 2:h=getStandardTable(e.huffmanDT+11);break;case 3:h=getCustomHuffmanTable(f,t,n);f++;break;default:throw new Jbig2Error("invalid Huffman DT selector")}if(e.refinement)throw new Jbig2Error("refinement with Huffman is not supported");return{symbolIDTable:a,tableFirstS:c,tableDeltaS:l,tableDeltaT:h}}(e,t,this.customTables,l.length,a)}const f=new DecodingContext(n,r,i),u=decodeTextRegion(e.huffman,e.refinement,s.width,s.height,e.defaultPixelValue,e.numberOfSymbolInstances,e.stripSize,l,h,e.transposed,e.dsOffset,e.referenceCorner,e.combinationOperator,o,e.refinementTemplate,e.refinementAt,f,e.logStripSize,a);this.drawBitmap(s,u)}onImmediateLosslessTextRegion(){this.onImmediateTextRegion(...arguments)}onPatternDictionary(e,t,n,r,i){let s=this.patterns;s||(this.patterns=s={});const o=new DecodingContext(n,r,i);s[t]=function decodePatternDictionary(e,t,n,r,i,s){const o=[];if(!e){o.push({x:-t,y:0});0===i&&o.push({x:-3,y:-1},{x:2,y:-2},{x:-2,y:-2})}const a=decodeBitmap(e,(r+1)*t,n,i,!1,null,o,s),c=[];for(let e=0;e<=r;e++){const r=[],i=t*e,s=i+t;for(let e=0;e<n;e++)r.push(a[e].subarray(i,s));c.push(r)}return c}(e.mmr,e.patternWidth,e.patternHeight,e.maxPatternIndex,e.template,o)}onImmediateHalftoneRegion(e,t,n,r,i){const s=this.patterns[t[0]],o=e.info,a=new DecodingContext(n,r,i),c=function decodeHalftoneRegion(e,t,n,r,i,s,o,a,c,l,h,f,u,d,m){if(o)throw new Jbig2Error("skip is not supported");if(0!==a)throw new Jbig2Error(`operator "${a}" is not supported in halftone region`);const g=[];let p,b,w;for(p=0;p<i;p++){w=new Uint8Array(r);s&&w.fill(s);g.push(w)}const y=t.length,C=t[0],x=C[0].length,_=C.length,B=log2(y),R=[];if(!e){R.push({x:n<=1?3:2,y:-1});0===n&&R.push({x:-3,y:-1},{x:2,y:-2},{x:-2,y:-2})}const S=[];let A,k,I,v,D,M,T,P,E,U,L;e&&(A=new Reader(m.data,m.start,m.end));for(p=B-1;p>=0;p--){k=e?decodeMMRBitmap(A,c,l,!0):decodeBitmap(!1,c,l,n,!1,null,R,m);S[p]=k}for(I=0;I<l;I++)for(v=0;v<c;v++){D=0;M=0;for(b=B-1;b>=0;b--){D^=S[b][I][v];M|=D<<b}T=t[M];P=h+I*d+v*u>>8;E=f+I*u-v*d>>8;if(P>=0&&P+x<=r&&E>=0&&E+_<=i)for(p=0;p<_;p++){L=g[E+p];U=T[p];for(b=0;b<x;b++)L[P+b]|=U[b]}else{let e,t;for(p=0;p<_;p++){t=E+p;if(!(t<0||t>=i)){L=g[t];U=T[p];for(b=0;b<x;b++){e=P+b;e>=0&&e<r&&(L[e]|=U[b])}}}}}return g}(e.mmr,s,e.template,o.width,o.height,e.defaultPixelValue,e.enableSkip,e.combinationOperator,e.gridWidth,e.gridHeight,e.gridOffsetX,e.gridOffsetY,e.gridVectorX,e.gridVectorY,a);this.drawBitmap(o,c)}onImmediateLosslessHalftoneRegion(){this.onImmediateHalftoneRegion(...arguments)}onTables(e,t,n,r){let i=this.customTables;i||(this.customTables=i={});i[e]=function decodeTablesSegment(e,t,n){const r=e[t],i=4294967295&readUint32(e,t+1),s=4294967295&readUint32(e,t+5),o=new Reader(e,t+9,n),a=1+(r>>1&7),c=1+(r>>4&7),l=[];let h,f,u=i;do{h=o.readBits(a);f=o.readBits(c);l.push(new HuffmanLine([u,h,f,0]));u+=1<<f}while(u<s);h=o.readBits(a);l.push(new HuffmanLine([i-1,h,32,0,"lower"]));h=o.readBits(a);l.push(new HuffmanLine([s,h,32,0]));if(1&r){h=o.readBits(a);l.push(new HuffmanLine([h,0]))}return new HuffmanTable(l,!1)}(t,n,r)}}class HuffmanLine{constructor(e){if(2===e.length){this.isOOB=!0;this.rangeLow=0;this.prefixLength=e[0];this.rangeLength=0;this.prefixCode=e[1];this.isLowerRange=!1}else{this.isOOB=!1;this.rangeLow=e[0];this.prefixLength=e[1];this.rangeLength=e[2];this.prefixCode=e[3];this.isLowerRange="lower"===e[4]}}}class HuffmanTreeNode{constructor(e){this.children=[];if(e){this.isLeaf=!0;this.rangeLength=e.rangeLength;this.rangeLow=e.rangeLow;this.isLowerRange=e.isLowerRange;this.isOOB=e.isOOB}else this.isLeaf=!1}buildTree(e,t){const n=e.prefixCode>>t&1;if(t<=0)this.children[n]=new HuffmanTreeNode(e);else{let r=this.children[n];r||(this.children[n]=r=new HuffmanTreeNode(null));r.buildTree(e,t-1)}}decodeNode(e){if(this.isLeaf){if(this.isOOB)return null;const t=e.readBits(this.rangeLength);return this.rangeLow+(this.isLowerRange?-t:t)}const t=this.children[e.readBit()];if(!t)throw new Jbig2Error("invalid Huffman data");return t.decodeNode(e)}}class HuffmanTable{constructor(e,t){t||this.assignPrefixCodes(e);this.rootNode=new HuffmanTreeNode(null);for(let t=0,n=e.length;t<n;t++){const n=e[t];n.prefixLength>0&&this.rootNode.buildTree(n,n.prefixLength-1)}}decode(e){return this.rootNode.decodeNode(e)}assignPrefixCodes(e){const t=e.length;let n=0;for(let r=0;r<t;r++)n=Math.max(n,e[r].prefixLength);const r=new Uint32Array(n+1);for(let n=0;n<t;n++)r[e[n].prefixLength]++;let i,s,o,a=1,c=0;r[0]=0;for(;a<=n;){c=c+r[a-1]<<1;i=c;s=0;for(;s<t;){o=e[s];if(o.prefixLength===a){o.prefixCode=i;i++}s++}a++}}}const x={};function getStandardTable(e){let t,n=x[e];if(n)return n;switch(e){case 1:t=[[0,1,4,0],[16,2,8,2],[272,3,16,6],[65808,3,32,7]];break;case 2:t=[[0,1,0,0],[1,2,0,2],[2,3,0,6],[3,4,3,14],[11,5,6,30],[75,6,32,62],[6,63]];break;case 3:t=[[-256,8,8,254],[0,1,0,0],[1,2,0,2],[2,3,0,6],[3,4,3,14],[11,5,6,30],[-257,8,32,255,"lower"],[75,7,32,126],[6,62]];break;case 4:t=[[1,1,0,0],[2,2,0,2],[3,3,0,6],[4,4,3,14],[12,5,6,30],[76,5,32,31]];break;case 5:t=[[-255,7,8,126],[1,1,0,0],[2,2,0,2],[3,3,0,6],[4,4,3,14],[12,5,6,30],[-256,7,32,127,"lower"],[76,6,32,62]];break;case 6:t=[[-2048,5,10,28],[-1024,4,9,8],[-512,4,8,9],[-256,4,7,10],[-128,5,6,29],[-64,5,5,30],[-32,4,5,11],[0,2,7,0],[128,3,7,2],[256,3,8,3],[512,4,9,12],[1024,4,10,13],[-2049,6,32,62,"lower"],[2048,6,32,63]];break;case 7:t=[[-1024,4,9,8],[-512,3,8,0],[-256,4,7,9],[-128,5,6,26],[-64,5,5,27],[-32,4,5,10],[0,4,5,11],[32,5,5,28],[64,5,6,29],[128,4,7,12],[256,3,8,1],[512,3,9,2],[1024,3,10,3],[-1025,5,32,30,"lower"],[2048,5,32,31]];break;case 8:t=[[-15,8,3,252],[-7,9,1,508],[-5,8,1,253],[-3,9,0,509],[-2,7,0,124],[-1,4,0,10],[0,2,1,0],[2,5,0,26],[3,6,0,58],[4,3,4,4],[20,6,1,59],[22,4,4,11],[38,4,5,12],[70,5,6,27],[134,5,7,28],[262,6,7,60],[390,7,8,125],[646,6,10,61],[-16,9,32,510,"lower"],[1670,9,32,511],[2,1]];break;case 9:t=[[-31,8,4,252],[-15,9,2,508],[-11,8,2,253],[-7,9,1,509],[-5,7,1,124],[-3,4,1,10],[-1,3,1,2],[1,3,1,3],[3,5,1,26],[5,6,1,58],[7,3,5,4],[39,6,2,59],[43,4,5,11],[75,4,6,12],[139,5,7,27],[267,5,8,28],[523,6,8,60],[779,7,9,125],[1291,6,11,61],[-32,9,32,510,"lower"],[3339,9,32,511],[2,0]];break;case 10:t=[[-21,7,4,122],[-5,8,0,252],[-4,7,0,123],[-3,5,0,24],[-2,2,2,0],[2,5,0,25],[3,6,0,54],[4,7,0,124],[5,8,0,253],[6,2,6,1],[70,5,5,26],[102,6,5,55],[134,6,6,56],[198,6,7,57],[326,6,8,58],[582,6,9,59],[1094,6,10,60],[2118,7,11,125],[-22,8,32,254,"lower"],[4166,8,32,255],[2,2]];break;case 11:t=[[1,1,0,0],[2,2,1,2],[4,4,0,12],[5,4,1,13],[7,5,1,28],[9,5,2,29],[13,6,2,60],[17,7,2,122],[21,7,3,123],[29,7,4,124],[45,7,5,125],[77,7,6,126],[141,7,32,127]];break;case 12:t=[[1,1,0,0],[2,2,0,2],[3,3,1,6],[5,5,0,28],[6,5,1,29],[8,6,1,60],[10,7,0,122],[11,7,1,123],[13,7,2,124],[17,7,3,125],[25,7,4,126],[41,8,5,254],[73,8,32,255]];break;case 13:t=[[1,1,0,0],[2,3,0,4],[3,4,0,12],[4,5,0,28],[5,4,1,13],[7,3,3,5],[15,6,1,58],[17,6,2,59],[21,6,3,60],[29,6,4,61],[45,6,5,62],[77,7,6,126],[141,7,32,127]];break;case 14:t=[[-2,3,0,4],[-1,3,0,5],[0,1,0,0],[1,3,0,6],[2,3,0,7]];break;case 15:t=[[-24,7,4,124],[-8,6,2,60],[-4,5,1,28],[-2,4,0,12],[-1,3,0,4],[0,1,0,0],[1,3,0,5],[2,4,0,13],[3,5,1,29],[5,6,2,61],[9,7,4,125],[-25,7,32,126,"lower"],[25,7,32,127]];break;default:throw new Jbig2Error(`standard table B.${e} does not exist`)}for(let e=0,n=t.length;e<n;e++)t[e]=new HuffmanLine(t[e]);n=new HuffmanTable(t,!0);x[e]=n;return n}class Reader{constructor(e,t,n){this.data=e;this.start=t;this.end=n;this.position=t;this.shift=-1;this.currentByte=0}readBit(){if(this.shift<0){if(this.position>=this.end)throw new Jbig2Error("end of data while reading bit");this.currentByte=this.data[this.position++];this.shift=7}const e=this.currentByte>>this.shift&1;this.shift--;return e}readBits(e){let t,n=0;for(t=e-1;t>=0;t--)n|=this.readBit()<<t;return n}byteAlign(){this.shift=-1}next(){return this.position>=this.end?-1:this.data[this.position++]}}function getCustomHuffmanTable(e,t,n){let r=0;for(let i=0,s=t.length;i<s;i++){const s=n[t[i]];if(s){if(e===r)return s;r++}}throw new Jbig2Error("can't find custom Huffman table")}function readUncompressedBitmap(e,t,n){const r=[];for(let i=0;i<n;i++){const n=new Uint8Array(t);r.push(n);for(let r=0;r<t;r++)n[r]=e.readBit();e.byteAlign()}return r}function decodeMMRBitmap(e,t,n,r){const i=new CCITTFaxDecoder(e,{K:-1,Columns:t,Rows:n,BlackIs1:!0,EndOfBlock:r}),s=[];let o,a=!1;for(let e=0;e<n;e++){const e=new Uint8Array(t);s.push(e);let n=-1;for(let r=0;r<t;r++){if(n<0){o=i.readNextChar();if(-1===o){o=0;a=!0}n=7}e[r]=o>>n&1;n--}}if(r&&!a){const e=5;for(let t=0;t<e&&-1!==i.readNextChar();t++);}return s}class Jbig2Image{parseChunks(e){return function parseJbig2Chunks(e){const t=new SimpleSegmentVisitor;for(let n=0,r=e.length;n<r;n++){const r=e[n];processSegments(readSegments({},r.data,r.start,r.end),t)}return t.buffer}(e)}parse(e){const{imgData:t,width:n,height:r}=function parseJbig2(e){const t=e.length;let n=0;if(151!==e[n]||74!==e[n+1]||66!==e[n+2]||50!==e[n+3]||13!==e[n+4]||10!==e[n+5]||26!==e[n+6]||10!==e[n+7])throw new Jbig2Error("parseJbig2 - invalid header.");const r=Object.create(null);n+=8;const i=e[n++];r.randomAccess=!(1&i);if(!(2&i)){r.numberOfPages=readUint32(e,n);n+=4}const s=readSegments(r,e,n,t),o=new SimpleSegmentVisitor;processSegments(s,o);const{width:a,height:c}=o.currentPageInfo,l=o.buffer,h=new Uint8ClampedArray(a*c);let f=0,u=0;for(let e=0;e<c;e++){let e,t=0;for(let n=0;n<a;n++){if(!t){t=128;e=l[u++]}h[f++]=e&t?0:255;t>>=1}}return{imgData:h,width:a,height:c}}(e);this.width=n;this.height=r;return t}}class ColorSpace{static#n=new Uint8ClampedArray(3);constructor(e,t){this.name=e;this.numComps=t}getRgb(e,t,n=new Uint8ClampedArray(3)){this.getRgbItem(e,t,n,0);return n}getRgbHex(e,t){const n=this.getRgb(e,t,ColorSpace.#n);return util_Util.makeHexColor(n[0],n[1],n[2])}getRgbItem(e,t,n,r){unreachable("Should not call ColorSpace.getRgbItem")}getRgbBuffer(e,t,n,r,i,s,o){unreachable("Should not call ColorSpace.getRgbBuffer")}getOutputLength(e,t){unreachable("Should not call ColorSpace.getOutputLength")}isPassthrough(e){return!1}isDefaultDecode(e,t){return ColorSpace.isDefaultDecode(e,this.numComps)}fillRgb(e,t,n,r,i,s,o,a,c){const l=t*n;let h=null;const f=1<<o,u=n!==i||t!==r;if(this.isPassthrough(o))h=a;else if(1===this.numComps&&l>f&&"DeviceGray"!==this.name&&"DeviceRGB"!==this.name){const t=o<=8?new Uint8Array(f):new Uint16Array(f);for(let e=0;e<f;e++)t[e]=e;const n=new Uint8ClampedArray(3*f);this.getRgbBuffer(t,0,f,n,0,o,0);if(u){h=new Uint8Array(3*l);let e=0;for(let t=0;t<l;++t){const r=3*a[t];h[e++]=n[r];h[e++]=n[r+1];h[e++]=n[r+2]}}else{let t=0;for(let r=0;r<l;++r){const i=3*a[r];e[t++]=n[i];e[t++]=n[i+1];e[t++]=n[i+2];t+=c}}}else if(u){h=new Uint8ClampedArray(3*l);this.getRgbBuffer(a,0,l,h,0,o,0)}else this.getRgbBuffer(a,0,r*s,e,0,o,c);if(h)if(u)!function resizeRgbImage(e,t,n,r,i,s,o){o=1!==o?0:o;const a=n/i,c=r/s;let l,h=0;const f=new Uint16Array(i),u=3*n;for(let e=0;e<i;e++)f[e]=3*Math.floor(e*a);for(let n=0;n<s;n++){const r=Math.floor(n*c)*u;for(let n=0;n<i;n++){l=r+f[n];t[h++]=e[l++];t[h++]=e[l++];t[h++]=e[l++];h+=o}}}(h,e,t,n,r,i,c);else{let t=0,n=0;for(let i=0,o=r*s;i<o;i++){e[t++]=h[n++];e[t++]=h[n++];e[t++]=h[n++];t+=c}}}get usesZeroToOneRange(){return shadow(this,"usesZeroToOneRange",!0)}static isDefaultDecode(e,t){if(!Array.isArray(e))return!0;if(2*t!==e.length){util_warn("The decode map is not the correct length");return!0}for(let t=0,n=e.length;t<n;t+=2)if(0!==e[t]||1!==e[t+1])return!1;return!0}}class AlternateCS extends ColorSpace{constructor(e,t,n){super("Alternate",e);this.base=t;this.tintFn=n;this.tmpBuf=new Float32Array(t.numComps)}getRgbItem(e,t,n,r){const i=this.tmpBuf;this.tintFn(e,t,i,0);this.base.getRgbItem(i,0,n,r)}getRgbBuffer(e,t,n,r,i,s,o){const a=this.tintFn,c=this.base,l=1/((1<<s)-1),h=c.numComps,f=c.usesZeroToOneRange,u=(c.isPassthrough(8)||!f)&&0===o;let d=u?i:0;const m=u?r:new Uint8ClampedArray(h*n),g=this.numComps,p=new Float32Array(g),b=new Float32Array(h);let w,y;for(w=0;w<n;w++){for(y=0;y<g;y++)p[y]=e[t++]*l;a(p,0,b,0);if(f)for(y=0;y<h;y++)m[d++]=255*b[y];else{c.getRgbItem(b,0,m,d);d+=h}}u||c.getRgbBuffer(m,0,n,r,i,8,o)}getOutputLength(e,t){return this.base.getOutputLength(e*this.base.numComps/this.numComps,t)}}class PatternCS extends ColorSpace{constructor(e){super("Pattern",null);this.base=e}isDefaultDecode(e,t){unreachable("Should not call PatternCS.isDefaultDecode")}}class IndexedCS extends ColorSpace{constructor(e,t,n){super("Indexed",1);this.base=e;this.highVal=t;const r=e.numComps*(t+1);this.lookup=new Uint8Array(r);if(n instanceof base_stream_BaseStream){const e=n.getBytes(r);this.lookup.set(e)}else{if("string"!=typeof n)throw new FormatError(`IndexedCS - unrecognized lookup table: ${n}`);for(let e=0;e<r;++e)this.lookup[e]=255&n.charCodeAt(e)}}getRgbItem(e,t,n,r){const{base:i,highVal:s,lookup:o}=this,a=MathClamp(Math.round(e[t]),0,s)*i.numComps;i.getRgbBuffer(o,a,1,n,r,8,0)}getRgbBuffer(e,t,n,r,i,s,o){const{base:a,highVal:c,lookup:l}=this,{numComps:h}=a,f=a.getOutputLength(h,o);for(let s=0;s<n;++s){const n=MathClamp(Math.round(e[t++]),0,c)*h;a.getRgbBuffer(l,n,1,r,i,8,o);i+=f}}getOutputLength(e,t){return this.base.getOutputLength(e*this.base.numComps,t)}isDefaultDecode(e,t){if(!Array.isArray(e))return!0;if(2!==e.length){util_warn("Decode map length is not correct");return!0}if(!Number.isInteger(t)||t<1){util_warn("Bits per component is not correct");return!0}return 0===e[0]&&e[1]===(1<<t)-1}}class DeviceGrayCS extends ColorSpace{constructor(){super("DeviceGray",1)}getRgbItem(e,t,n,r){const i=255*e[t];n[r]=n[r+1]=n[r+2]=i}getRgbBuffer(e,t,n,r,i,s,o){const a=255/((1<<s)-1);let c=t,l=i;for(let t=0;t<n;++t){const t=a*e[c++];r[l++]=t;r[l++]=t;r[l++]=t;l+=o}}getOutputLength(e,t){return e*(3+t)}}class DeviceRgbCS extends ColorSpace{constructor(){super("DeviceRGB",3)}getRgbItem(e,t,n,r){n[r]=255*e[t];n[r+1]=255*e[t+1];n[r+2]=255*e[t+2]}getRgbBuffer(e,t,n,r,i,s,o){if(8===s&&0===o){r.set(e.subarray(t,t+3*n),i);return}const a=255/((1<<s)-1);let c=t,l=i;for(let t=0;t<n;++t){r[l++]=a*e[c++];r[l++]=a*e[c++];r[l++]=a*e[c++];l+=o}}getOutputLength(e,t){return e*(3+t)/3|0}isPassthrough(e){return 8===e}}class DeviceRgbaCS extends ColorSpace{constructor(){super("DeviceRGBA",4)}getOutputLength(e,t){return 4*e}isPassthrough(e){return 8===e}fillRgb(e,t,n,r,i,s,o,a,c){n!==i||t!==r?function resizeRgbaImage(e,t,n,r,i,s,o){const a=n/i,c=r/s;let l=0;const h=new Uint16Array(i);if(1===o){for(let e=0;e<i;e++)h[e]=Math.floor(e*a);const r=new Uint32Array(e.buffer),o=new Uint32Array(t.buffer),f=util_FeatureTest.isLittleEndian?16777215:4294967040;for(let e=0;e<s;e++){const t=r.subarray(Math.floor(e*c)*n);for(let e=0;e<i;e++)o[l++]|=t[h[e]]&f}}else{const r=4,o=n*r;for(let e=0;e<i;e++)h[e]=Math.floor(e*a)*r;for(let n=0;n<s;n++){const r=e.subarray(Math.floor(n*c)*o);for(let e=0;e<i;e++){const n=h[e];t[l++]=r[n];t[l++]=r[n+1];t[l++]=r[n+2]}}}}(a,e,t,n,r,i,c):function copyRgbaImage(e,t,n){if(1===n){const n=new Uint32Array(e.buffer),r=new Uint32Array(t.buffer),i=util_FeatureTest.isLittleEndian?16777215:4294967040;for(let e=0,t=n.length;e<t;e++)r[e]|=n[e]&i}else{let n=0;for(let r=0,i=e.length;r<i;r+=4){t[n++]=e[r];t[n++]=e[r+1];t[n++]=e[r+2]}}}(a,e,c)}}class DeviceCmykCS extends ColorSpace{constructor(){super("DeviceCMYK",4)}#r(e,t,n,r,i){const s=e[t]*n,o=e[t+1]*n,a=e[t+2]*n,c=e[t+3]*n;r[i]=255+s*(-4.387332384609988*s+54.48615194189176*o+18.82290502165302*a+212.25662451639585*c-285.2331026137004)+o*(1.7149763477362134*o-5.6096736904047315*a+-17.873870861415444*c-5.497006427196366)+a*(-2.5217340131683033*a-21.248923337353073*c+17.5119270841813)+c*(-21.86122147463605*c-189.48180835922747);r[i+1]=255+s*(8.841041422036149*s+60.118027045597366*o+6.871425592049007*a+31.159100130055922*c-79.2970844816548)+o*(-15.310361306967817*o+17.575251261109482*a+131.35250912493976*c-190.9453302588951)+a*(4.444339102852739*a+9.8632861493405*c-24.86741582555878)+c*(-20.737325471181034*c-187.80453709719578);r[i+2]=255+s*(.8842522430003296*s+8.078677503112928*o+30.89978309703729*a-.23883238689178934*c-14.183576799673286)+o*(10.49593273432072*o+63.02378494754052*a+50.606957656360734*c-112.23884253719248)+a*(.03296041114873217*a+115.60384449646641*c-193.58209356861505)+c*(-22.33816807309886*c-180.12613974708367)}getRgbItem(e,t,n,r){this.#r(e,t,1,n,r)}getRgbBuffer(e,t,n,r,i,s,o){const a=1/((1<<s)-1);for(let s=0;s<n;s++){this.#r(e,t,a,r,i);t+=4;i+=3+o}}getOutputLength(e,t){return e/4*(3+t)|0}}class CalGrayCS extends ColorSpace{constructor(e,t,n){super("CalGray",1);if(!e)throw new FormatError("WhitePoint missing - required for color space CalGray");[this.XW,this.YW,this.ZW]=e;[this.XB,this.YB,this.ZB]=t||[0,0,0];this.G=n||1;if(this.XW<0||this.ZW<0||1!==this.YW)throw new FormatError(`Invalid WhitePoint components for ${this.name}, no fallback available`);if(this.XB<0||this.YB<0||this.ZB<0){info(`Invalid BlackPoint for ${this.name}, falling back to default.`);this.XB=this.YB=this.ZB=0}0===this.XB&&0===this.YB&&0===this.ZB||util_warn(`${this.name}, BlackPoint: XB: ${this.XB}, YB: ${this.YB}, ZB: ${this.ZB}, only default values are supported.`);if(this.G<1){info(`Invalid Gamma: ${this.G} for ${this.name}, falling back to default.`);this.G=1}}#r(e,t,n,r,i){const s=(e[t]*i)**this.G,o=this.YW*s,a=Math.max(295.8*o**.3333333333333333-40.8,0);n[r]=a;n[r+1]=a;n[r+2]=a}getRgbItem(e,t,n,r){this.#r(e,t,n,r,1)}getRgbBuffer(e,t,n,r,i,s,o){const a=1/((1<<s)-1);for(let s=0;s<n;++s){this.#r(e,t,r,i,a);t+=1;i+=3+o}}getOutputLength(e,t){return e*(3+t)}}class CalRGBCS extends ColorSpace{static#i=new Float32Array([.8951,.2664,-.1614,-.7502,1.7135,.0367,.0389,-.0685,1.0296]);static#s=new Float32Array([.9869929,-.1470543,.1599627,.4323053,.5183603,.0492912,-.0085287,.0400428,.9684867]);static#o=new Float32Array([3.2404542,-1.5371385,-.4985314,-.969266,1.8760108,.041556,.0556434,-.2040259,1.0572252]);static#a=new Float32Array([1,1,1]);static#c=new Float32Array(3);static#l=new Float32Array(3);static#h=new Float32Array(3);static#f=(24/116)**3/8;constructor(e,t,n,r){super("CalRGB",3);if(!e)throw new FormatError("WhitePoint missing - required for color space CalRGB");const[i,s,o]=this.whitePoint=e,[a,c,l]=this.blackPoint=t||new Float32Array(3);[this.GR,this.GG,this.GB]=n||new Float32Array([1,1,1]);[this.MXA,this.MYA,this.MZA,this.MXB,this.MYB,this.MZB,this.MXC,this.MYC,this.MZC]=r||new Float32Array([1,0,0,0,1,0,0,0,1]);if(i<0||o<0||1!==s)throw new FormatError(`Invalid WhitePoint components for ${this.name}, no fallback available`);if(a<0||c<0||l<0){info(`Invalid BlackPoint for ${this.name} [${a}, ${c}, ${l}], falling back to default.`);this.blackPoint=new Float32Array(3)}if(this.GR<0||this.GG<0||this.GB<0){info(`Invalid Gamma [${this.GR}, ${this.GG}, ${this.GB}] for ${this.name}, falling back to default.`);this.GR=this.GG=this.GB=1}}#u(e,t,n){n[0]=e[0]*t[0]+e[1]*t[1]+e[2]*t[2];n[1]=e[3]*t[0]+e[4]*t[1]+e[5]*t[2];n[2]=e[6]*t[0]+e[7]*t[1]+e[8]*t[2]}#d(e,t,n){n[0]=1*t[0]/e[0];n[1]=1*t[1]/e[1];n[2]=1*t[2]/e[2]}#m(e,t,n){n[0]=.95047*t[0]/e[0];n[1]=1*t[1]/e[1];n[2]=1.08883*t[2]/e[2]}#g(e){return e<=.0031308?MathClamp(12.92*e,0,1):e>=.99554525?1:MathClamp(1.055*e**(1/2.4)-.055,0,1)}#p(e){return e<0?-this.#p(-e):e>8?((e+16)/116)**3:e*CalRGBCS.#f}#b(e,t,n){if(0===e[0]&&0===e[1]&&0===e[2]){n[0]=t[0];n[1]=t[1];n[2]=t[2];return}const r=this.#p(0),i=(1-r)/(1-this.#p(e[0])),s=1-i,o=(1-r)/(1-this.#p(e[1])),a=1-o,c=(1-r)/(1-this.#p(e[2])),l=1-c;n[0]=t[0]*i+s;n[1]=t[1]*o+a;n[2]=t[2]*c+l}#w(e,t,n){if(1===e[0]&&1===e[2]){n[0]=t[0];n[1]=t[1];n[2]=t[2];return}const r=n;this.#u(CalRGBCS.#i,t,r);const i=CalRGBCS.#c;this.#d(e,r,i);this.#u(CalRGBCS.#s,i,n)}#y(e,t,n){const r=n;this.#u(CalRGBCS.#i,t,r);const i=CalRGBCS.#c;this.#m(e,r,i);this.#u(CalRGBCS.#s,i,n)}#r(e,t,n,r,i){const s=MathClamp(e[t]*i,0,1),o=MathClamp(e[t+1]*i,0,1),a=MathClamp(e[t+2]*i,0,1),c=1===s?1:s**this.GR,l=1===o?1:o**this.GG,h=1===a?1:a**this.GB,f=this.MXA*c+this.MXB*l+this.MXC*h,u=this.MYA*c+this.MYB*l+this.MYC*h,d=this.MZA*c+this.MZB*l+this.MZC*h,m=CalRGBCS.#l;m[0]=f;m[1]=u;m[2]=d;const g=CalRGBCS.#h;this.#w(this.whitePoint,m,g);const p=CalRGBCS.#l;this.#b(this.blackPoint,g,p);const b=CalRGBCS.#h;this.#y(CalRGBCS.#a,p,b);const w=CalRGBCS.#l;this.#u(CalRGBCS.#o,b,w);n[r]=255*this.#g(w[0]);n[r+1]=255*this.#g(w[1]);n[r+2]=255*this.#g(w[2])}getRgbItem(e,t,n,r){this.#r(e,t,n,r,1)}getRgbBuffer(e,t,n,r,i,s,o){const a=1/((1<<s)-1);for(let s=0;s<n;++s){this.#r(e,t,r,i,a);t+=3;i+=3+o}}getOutputLength(e,t){return e*(3+t)/3|0}}class LabCS extends ColorSpace{constructor(e,t,n){super("Lab",3);if(!e)throw new FormatError("WhitePoint missing - required for color space Lab");[this.XW,this.YW,this.ZW]=e;[this.amin,this.amax,this.bmin,this.bmax]=n||[-100,100,-100,100];[this.XB,this.YB,this.ZB]=t||[0,0,0];if(this.XW<0||this.ZW<0||1!==this.YW)throw new FormatError("Invalid WhitePoint components, no fallback available");if(this.XB<0||this.YB<0||this.ZB<0){info("Invalid BlackPoint, falling back to default");this.XB=this.YB=this.ZB=0}if(this.amin>this.amax||this.bmin>this.bmax){info("Invalid Range, falling back to defaults");this.amin=-100;this.amax=100;this.bmin=-100;this.bmax=100}}#C(e){return e>=6/29?e**3:108/841*(e-4/29)}#x(e,t,n,r){return n+e*(r-n)/t}#r(e,t,n,r,i){let s=e[t],o=e[t+1],a=e[t+2];if(!1!==n){s=this.#x(s,n,0,100);o=this.#x(o,n,this.amin,this.amax);a=this.#x(a,n,this.bmin,this.bmax)}o>this.amax?o=this.amax:o<this.amin&&(o=this.amin);a>this.bmax?a=this.bmax:a<this.bmin&&(a=this.bmin);const c=(s+16)/116,l=c+o/500,h=c-a/200,f=this.XW*this.#C(l),u=this.YW*this.#C(c),d=this.ZW*this.#C(h);let m,g,p;if(this.ZW<1){m=3.1339*f+-1.617*u+-.4906*d;g=-.9785*f+1.916*u+.0333*d;p=.072*f+-.229*u+1.4057*d}else{m=3.2406*f+-1.5372*u+-.4986*d;g=-.9689*f+1.8758*u+.0415*d;p=.0557*f+-.204*u+1.057*d}r[i]=255*Math.sqrt(m);r[i+1]=255*Math.sqrt(g);r[i+2]=255*Math.sqrt(p)}getRgbItem(e,t,n,r){this.#r(e,t,!1,n,r)}getRgbBuffer(e,t,n,r,i,s,o){const a=(1<<s)-1;for(let s=0;s<n;s++){this.#r(e,t,a,r,i);t+=3;i+=3+o}}getOutputLength(e,t){return e*(3+t)/3|0}isDefaultDecode(e,t){return!0}get usesZeroToOneRange(){return shadow(this,"usesZeroToOneRange",!1)}}class QCMS{static#_=null;static _memory=null;static _mustAddAlpha=!1;static _destBuffer=null;static _destOffset=0;static _destLength=0;static _cssColor="";static _makeHexColor=null;static get _memoryArray(){const e=this.#_;return e?.byteLength?e:this.#_=new Uint8Array(this._memory.buffer)}}let _;const B="undefined"!=typeof TextDecoder?new TextDecoder("utf-8",{ignoreBOM:!0,fatal:!0}):{decode:()=>{throw Error("TextDecoder not available")}};"undefined"!=typeof TextDecoder&&B.decode();let R=null;function getUint8ArrayMemory0(){null!==R&&0!==R.byteLength||(R=new Uint8Array(_.memory.buffer));return R}let S=0;function passArray8ToWasm0(e,t){const n=t(1*e.length,1)>>>0;getUint8ArrayMemory0().set(e,n/1);S=e.length;return n}const A=Object.freeze({RGB8:0,0:"RGB8",RGBA8:1,1:"RGBA8",BGRA8:2,2:"BGRA8",Gray8:3,3:"Gray8",GrayA8:4,4:"GrayA8",CMYK:5,5:"CMYK"}),k=Object.freeze({Perceptual:0,0:"Perceptual",RelativeColorimetric:1,1:"RelativeColorimetric",Saturation:2,2:"Saturation",AbsoluteColorimetric:3,3:"AbsoluteColorimetric"});function __wbg_get_imports(){const e={wbg:{}};e.wbg.__wbg_copyresult_b08ee7d273f295dd=function(e,t){!function copy_result(e,t){const{_mustAddAlpha:n,_destBuffer:r,_destOffset:i,_destLength:s,_memoryArray:o}=QCMS;if(t!==s)if(n)for(let n=e,s=e+t,a=i;n<s;n+=3,a+=4){r[a]=o[n];r[a+1]=o[n+1];r[a+2]=o[n+2];r[a+3]=255}else for(let n=e,s=e+t,a=i;n<s;n+=3,a+=4){r[a]=o[n];r[a+1]=o[n+1];r[a+2]=o[n+2]}else r.set(o.subarray(e,e+t),i)}(e>>>0,t>>>0)};e.wbg.__wbg_copyrgb_d60ce17bb05d9b67=function(e){!function copy_rgb(e){const{_destBuffer:t,_destOffset:n,_memoryArray:r}=QCMS;t[n]=r[e];t[n+1]=r[e+1];t[n+2]=r[e+2]}(e>>>0)};e.wbg.__wbg_makecssRGB_893bf0cd9fdb302d=function(e){!function make_cssRGB(e){const{_memoryArray:t}=QCMS;QCMS._cssColor=QCMS._makeHexColor(t[e],t[e+1],t[e+2])}(e>>>0)};e.wbg.__wbindgen_init_externref_table=function(){const e=_.__wbindgen_export_0,t=e.grow(4);e.set(0,void 0);e.set(t+0,void 0);e.set(t+1,null);e.set(t+2,!0);e.set(t+3,!1)};e.wbg.__wbindgen_throw=function(e,t){throw new Error(function getStringFromWasm0(e,t){e>>>=0;return B.decode(getUint8ArrayMemory0().subarray(e,e+t))}(e,t))};return e}function __wbg_finalize_init(e,t){_=e.exports;__wbg_init.__wbindgen_wasm_module=t;R=null;_.__wbindgen_start();return _}async function __wbg_init(e){if(void 0!==_)return _;void 0!==e&&(Object.getPrototypeOf(e)===Object.prototype?({module_or_path:e}=e):console.warn("using deprecated parameters for the initialization function; pass a single object instead"));const t=__wbg_get_imports();("string"==typeof e||"function"==typeof Request&&e instanceof Request||"function"==typeof URL&&e instanceof URL)&&(e=fetch(e));const{instance:n,module:r}=await async function __wbg_load(e,t){if("function"==typeof Response&&e instanceof Response){if("function"==typeof WebAssembly.instantiateStreaming)try{return await WebAssembly.instantiateStreaming(e,t)}catch(t){if("application/wasm"==e.headers.get("Content-Type"))throw t;console.warn("`WebAssembly.instantiateStreaming` failed because your server does not serve Wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\n",t)}const n=await e.arrayBuffer();return await WebAssembly.instantiate(n,t)}{const n=await WebAssembly.instantiate(e,t);return n instanceof WebAssembly.Instance?{instance:n,module:e}:n}}(await e,t);return __wbg_finalize_init(n,r)}function fetchSync(e){const t=new XMLHttpRequest;t.open("GET",e,!1);t.responseType="arraybuffer";t.send(null);return t.response}class IccColorSpace extends ColorSpace{#B;#R;static#S=!0;static#A=null;static#k=new FinalizationRegistry((e=>{!function qcms_drop_transformer(e){_.qcms_drop_transformer(e)}(e)}));constructor(e,t,n){if(!IccColorSpace.isUsable)throw new Error("No ICC color space support");super(t,n);let r;switch(n){case 1:r=A.Gray8;this.#R=(e,t,n)=>function qcms_convert_one(e,t,n){_.qcms_convert_one(e,t,n)}(this.#B,255*e[t],n);break;case 3:r=A.RGB8;this.#R=(e,t,n)=>function qcms_convert_three(e,t,n,r,i){_.qcms_convert_three(e,t,n,r,i)}(this.#B,255*e[t],255*e[t+1],255*e[t+2],n);break;case 4:r=A.CMYK;this.#R=(e,t,n)=>function qcms_convert_four(e,t,n,r,i,s){_.qcms_convert_four(e,t,n,r,i,s)}(this.#B,255*e[t],255*e[t+1],255*e[t+2],255*e[t+3],n);break;default:throw new Error(`Unsupported number of components: ${n}`)}this.#B=function qcms_transformer_from_memory(e,t,n){const r=passArray8ToWasm0(e,_.__wbindgen_malloc),i=S;return _.qcms_transformer_from_memory(r,i,t,n)>>>0}(e,r,k.Perceptual);if(!this.#B)throw new Error("Failed to create ICC color space");IccColorSpace.#k.register(this,this.#B)}getRgbHex(e,t){this.#R(e,t,!0);return QCMS._cssColor}getRgbItem(e,t,n,r){QCMS._destBuffer=n;QCMS._destOffset=r;QCMS._destLength=3;this.#R(e,t,!1);QCMS._destBuffer=null}getRgbBuffer(e,t,n,r,i,s,o){e=e.subarray(t,t+n*this.numComps);if(8!==s){const t=255/((1<<s)-1);for(let n=0,r=e.length;n<r;n++)e[n]*=t}QCMS._mustAddAlpha=o&&r.buffer===e.buffer;QCMS._destBuffer=r;QCMS._destOffset=i;QCMS._destLength=n*(3+o);!function qcms_convert_array(e,t){const n=passArray8ToWasm0(t,_.__wbindgen_malloc),r=S;_.qcms_convert_array(e,n,r)}(this.#B,e);QCMS._mustAddAlpha=!1;QCMS._destBuffer=null}getOutputLength(e,t){return e/this.numComps*(3+t)|0}static setOptions({useWasm:e,useWorkerFetch:t,wasmUrl:n}){if(t){this.#S=e;this.#A=n}else this.#S=!1}static get isUsable(){let e=!1;if(this.#S)if(this.#A)try{this._module=function initSync(e){if(void 0!==_)return _;void 0!==e&&(Object.getPrototypeOf(e)===Object.prototype?({module:e}=e):console.warn("using deprecated parameters for `initSync()`; pass a single object instead"));const t=__wbg_get_imports();e instanceof WebAssembly.Module||(e=new WebAssembly.Module(e));return __wbg_finalize_init(new WebAssembly.Instance(e,t),e)}({module:fetchSync(`${this.#A}qcms_bg.wasm`)});e=!!this._module;QCMS._memory=this._module.memory;QCMS._makeHexColor=util_Util.makeHexColor}catch(e){util_warn(`ICCBased color space: "${e}".`)}else util_warn("No ICC color space support due to missing `wasmUrl` API option");return shadow(this,"isUsable",e)}}class CmykICCBasedCS extends IccColorSpace{static#I;constructor(){super(new Uint8Array(fetchSync(`${CmykICCBasedCS.#I}CGATS001Compat-v2-micro.icc`)),"DeviceCMYK",4)}static setOptions({iccUrl:e}){this.#I=e}static get isUsable(){let e=!1;IccColorSpace.isUsable&&(this.#I?e=!0:util_warn("No CMYK ICC profile support due to missing `iccUrl` API option"));return shadow(this,"isUsable",e)}}class ColorSpaceUtils{static parse({cs:e,xref:t,resources:n=null,pdfFunctionFactory:r,globalColorSpaceCache:i,localColorSpaceCache:s,asyncIfNotCached:o=!1}){const a={xref:t,resources:n,pdfFunctionFactory:r,globalColorSpaceCache:i,localColorSpaceCache:s};let c,l,h;if(e instanceof primitives_Ref){l=e;const n=i.getByRef(l)||s.getByRef(l);if(n)return n;e=t.fetch(e)}if(e instanceof Name){c=e.name;const t=s.getByName(c);if(t)return t}try{h=this.#v(e,a)}catch(e){if(o&&!(e instanceof MissingDataException))return Promise.reject(e);throw e}if(c||l){s.set(c,l,h);l&&i.set(null,l,h)}return o?Promise.resolve(h):h}static#D(e,t){const{globalColorSpaceCache:n}=t;let r;if(e instanceof primitives_Ref){r=e;const t=n.getByRef(r);if(t)return t}const i=this.#v(e,t);r&&n.set(null,r,i);return i}static#v(e,t){const{xref:n,resources:r,pdfFunctionFactory:i,globalColorSpaceCache:s}=t;if((e=n.fetchIfRef(e))instanceof Name)switch(e.name){case"G":case"DeviceGray":return this.gray;case"RGB":case"DeviceRGB":return this.rgb;case"DeviceRGBA":return this.rgba;case"CMYK":case"DeviceCMYK":return this.cmyk;case"Pattern":return new PatternCS(null);default:if(r instanceof primitives_Dict){const n=r.get("ColorSpace");if(n instanceof primitives_Dict){const r=n.get(e.name);if(r){if(r instanceof Name)return this.#v(r,t);e=r;break}}}util_warn(`Unrecognized ColorSpace: ${e.name}`);return this.gray}if(Array.isArray(e)){const r=n.fetchIfRef(e[0]).name;let o,a,c,l,h,f;switch(r){case"G":case"DeviceGray":return this.gray;case"RGB":case"DeviceRGB":return this.rgb;case"CMYK":case"DeviceCMYK":return this.cmyk;case"CalGray":o=n.fetchIfRef(e[1]);l=o.getArray("WhitePoint");h=o.getArray("BlackPoint");f=o.get("Gamma");return new CalGrayCS(l,h,f);case"CalRGB":o=n.fetchIfRef(e[1]);l=o.getArray("WhitePoint");h=o.getArray("BlackPoint");f=o.getArray("Gamma");const u=o.getArray("Matrix");return new CalRGBCS(l,h,f,u);case"ICCBased":const d=e[1]instanceof primitives_Ref;if(d){const t=s.getByRef(e[1]);if(t)return t}const m=n.fetchIfRef(e[1]),g=m.dict;a=g.get("N");if(IccColorSpace.isUsable)try{const t=new IccColorSpace(m.getBytes(),"ICCBased",a);d&&s.set(null,e[1],t);return t}catch(t){if(t instanceof MissingDataException)throw t;util_warn(`ICCBased color space (${e[1]}): "${t}".`)}const p=g.getRaw("Alternate");if(p){const e=this.#D(p,t);if(e.numComps===a)return e;util_warn("ICCBased color space: Ignoring incorrect /Alternate entry.")}if(1===a)return this.gray;if(3===a)return this.rgb;if(4===a)return this.cmyk;break;case"Pattern":c=e[1]||null;c&&(c=this.#D(c,t));return new PatternCS(c);case"I":case"Indexed":c=this.#D(e[1],t);const b=MathClamp(n.fetchIfRef(e[2]),0,255),w=n.fetchIfRef(e[3]);return new IndexedCS(c,b,w);case"Separation":case"DeviceN":const y=n.fetchIfRef(e[1]);a=Array.isArray(y)?y.length:1;c=this.#D(e[2],t);const C=i.create(e[3]);return new AlternateCS(a,c,C);case"Lab":o=n.fetchIfRef(e[1]);l=o.getArray("WhitePoint");h=o.getArray("BlackPoint");const x=o.getArray("Range");return new LabCS(l,h,x);default:util_warn(`Unimplemented ColorSpace object: ${r}`);return this.gray}}util_warn(`Unrecognized ColorSpace object: ${e}`);return this.gray}static get gray(){return shadow(this,"gray",new DeviceGrayCS)}static get rgb(){return shadow(this,"rgb",new DeviceRgbCS)}static get rgba(){return shadow(this,"rgba",new DeviceRgbaCS)}static get cmyk(){if(CmykICCBasedCS.isUsable)try{return shadow(this,"cmyk",new CmykICCBasedCS)}catch{util_warn("CMYK fallback: DeviceCMYK")}return shadow(this,"cmyk",new DeviceCmykCS)}}class JpegError extends n{constructor(e){super(e,"JpegError")}}class DNLMarkerError extends n{constructor(e,t){super(e,"DNLMarkerError");this.scanLines=t}}class EOIMarkerError extends n{constructor(e){super(e,"EOIMarkerError")}}const I=new Uint8Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]),v=4017,D=799,M=3406,T=2276,P=1567,E=3784,U=5793,L=2896;function buildHuffmanTable(e,t){let n,r,i=0,s=16;for(;s>0&&!e[s-1];)s--;const o=[{children:[],index:0}];let a,c=o[0];for(n=0;n<s;n++){for(r=0;r<e[n];r++){c=o.pop();c.children[c.index]=t[i];for(;c.index>0;)c=o.pop();c.index++;o.push(c);for(;o.length<=n;){o.push(a={children:[],index:0});c.children[c.index]=a.children;c=a}i++}if(n+1<s){o.push(a={children:[],index:0});c.children[c.index]=a.children;c=a}}return o[0].children}function getBlockBufferOffset(e,t,n){return 64*((e.blocksPerLine+1)*t+n)}function decodeScan(e,t,n,r,i,s,o,a,c,l=!1){const h=n.mcusPerLine,f=n.progressive,u=t;let d=0,m=0;function readBit(){if(m>0){m--;return d>>m&1}d=e[t++];if(255===d){const r=e[t++];if(r){if(220===r&&l){const r=readUint16(e,t+=2);t+=2;if(r>0&&r!==n.scanLines)throw new DNLMarkerError("Found DNL marker (0xFFDC) while parsing scan data",r)}else if(217===r){if(l){const e=w*(8===n.precision?8:0);if(e>0&&Math.round(n.scanLines/e)>=5)throw new DNLMarkerError("Found EOI marker (0xFFD9) while parsing scan data, possibly caused by incorrect `scanLines` parameter",e)}throw new EOIMarkerError("Found EOI marker (0xFFD9) while parsing scan data")}throw new JpegError(`unexpected marker ${(d<<8|r).toString(16)}`)}}m=7;return d>>>7}function decodeHuffman(e){let t=e;for(;;){t=t[readBit()];switch(typeof t){case"number":return t;case"object":continue}throw new JpegError("invalid huffman sequence")}}function receive(e){let t=0;for(;e>0;){t=t<<1|readBit();e--}return t}function receiveAndExtend(e){if(1===e)return 1===readBit()?1:-1;const t=receive(e);return t>=1<<e-1?t:t+(-1<<e)+1}let g=0;let p,b=0;let w=0;function decodeMcu(e,t,n,r,i){const s=n%h;w=(n/h|0)*e.v+r;const o=s*e.h+i;t(e,getBlockBufferOffset(e,w,o))}function decodeBlock(e,t,n){w=n/e.blocksPerLine|0;const r=n%e.blocksPerLine;t(e,getBlockBufferOffset(e,w,r))}const y=r.length;let C,x,_,B,R,S;S=f?0===s?0===a?function decodeDCFirst(e,t){const n=decodeHuffman(e.huffmanTableDC),r=0===n?0:receiveAndExtend(n)<<c;e.blockData[t]=e.pred+=r}:function decodeDCSuccessive(e,t){e.blockData[t]|=readBit()<<c}:0===a?function decodeACFirst(e,t){if(g>0){g--;return}let n=s;const r=o;for(;n<=r;){const r=decodeHuffman(e.huffmanTableAC),i=15&r,s=r>>4;if(0===i){if(s<15){g=receive(s)+(1<<s)-1;break}n+=16;continue}n+=s;const o=I[n];e.blockData[t+o]=receiveAndExtend(i)*(1<<c);n++}}:function decodeACSuccessive(e,t){let n=s;const r=o;let i,a,l=0;for(;n<=r;){const r=t+I[n],s=e.blockData[r]<0?-1:1;switch(b){case 0:a=decodeHuffman(e.huffmanTableAC);i=15&a;l=a>>4;if(0===i)if(l<15){g=receive(l)+(1<<l);b=4}else{l=16;b=1}else{if(1!==i)throw new JpegError("invalid ACn encoding");p=receiveAndExtend(i);b=l?2:3}continue;case 1:case 2:if(e.blockData[r])e.blockData[r]+=s*(readBit()<<c);else{l--;0===l&&(b=2===b?3:0)}break;case 3:if(e.blockData[r])e.blockData[r]+=s*(readBit()<<c);else{e.blockData[r]=p<<c;b=0}break;case 4:e.blockData[r]&&(e.blockData[r]+=s*(readBit()<<c))}n++}if(4===b){g--;0===g&&(b=0)}}:function decodeBaseline(e,t){const n=decodeHuffman(e.huffmanTableDC),r=0===n?0:receiveAndExtend(n);e.blockData[t]=e.pred+=r;let i=1;for(;i<64;){const n=decodeHuffman(e.huffmanTableAC),r=15&n,s=n>>4;if(0===r){if(s<15)break;i+=16;continue}i+=s;const o=I[i];e.blockData[t+o]=receiveAndExtend(r);i++}};let A,k=0;const v=1===y?r[0].blocksPerLine*r[0].blocksPerColumn:h*n.mcusPerColumn;let D,M;for(;k<=v;){const n=i?Math.min(v-k,i):v;if(n>0){for(x=0;x<y;x++)r[x].pred=0;g=0;if(1===y){C=r[0];for(R=0;R<n;R++){decodeBlock(C,S,k);k++}}else for(R=0;R<n;R++){for(x=0;x<y;x++){C=r[x];D=C.h;M=C.v;for(_=0;_<M;_++)for(B=0;B<D;B++)decodeMcu(C,S,k,_,B)}k++}}m=0;A=findNextFileMarker(e,t);if(!A)break;if(A.invalid){util_warn(`decodeScan - ${n>0?"unexpected":"excessive"} MCU data, current marker is: ${A.invalid}`);t=A.offset}if(!(A.marker>=65488&&A.marker<=65495))break;t+=2}return t-u}function quantizeAndInverse(e,t,n){const r=e.quantizationTable,i=e.blockData;let s,o,a,c,l,h,f,u,d,m,g,p,b,w,y,C,x;if(!r)throw new JpegError("missing required Quantization Table.");for(let e=0;e<64;e+=8){d=i[t+e];m=i[t+e+1];g=i[t+e+2];p=i[t+e+3];b=i[t+e+4];w=i[t+e+5];y=i[t+e+6];C=i[t+e+7];d*=r[e];if(m|g|p|b|w|y|C){m*=r[e+1];g*=r[e+2];p*=r[e+3];b*=r[e+4];w*=r[e+5];y*=r[e+6];C*=r[e+7];s=U*d+128>>8;o=U*b+128>>8;a=g;c=y;l=L*(m-C)+128>>8;u=L*(m+C)+128>>8;h=p<<4;f=w<<4;s=s+o+1>>1;o=s-o;x=a*E+c*P+128>>8;a=a*P-c*E+128>>8;c=x;l=l+f+1>>1;f=l-f;u=u+h+1>>1;h=u-h;s=s+c+1>>1;c=s-c;o=o+a+1>>1;a=o-a;x=l*T+u*M+2048>>12;l=l*M-u*T+2048>>12;u=x;x=h*D+f*v+2048>>12;h=h*v-f*D+2048>>12;f=x;n[e]=s+u;n[e+7]=s-u;n[e+1]=o+f;n[e+6]=o-f;n[e+2]=a+h;n[e+5]=a-h;n[e+3]=c+l;n[e+4]=c-l}else{x=U*d+512>>10;n[e]=x;n[e+1]=x;n[e+2]=x;n[e+3]=x;n[e+4]=x;n[e+5]=x;n[e+6]=x;n[e+7]=x}}for(let e=0;e<8;++e){d=n[e];m=n[e+8];g=n[e+16];p=n[e+24];b=n[e+32];w=n[e+40];y=n[e+48];C=n[e+56];if(m|g|p|b|w|y|C){s=U*d+2048>>12;o=U*b+2048>>12;a=g;c=y;l=L*(m-C)+2048>>12;u=L*(m+C)+2048>>12;h=p;f=w;s=4112+(s+o+1>>1);o=s-o;x=a*E+c*P+2048>>12;a=a*P-c*E+2048>>12;c=x;l=l+f+1>>1;f=l-f;u=u+h+1>>1;h=u-h;s=s+c+1>>1;c=s-c;o=o+a+1>>1;a=o-a;x=l*T+u*M+2048>>12;l=l*M-u*T+2048>>12;u=x;x=h*D+f*v+2048>>12;h=h*v-f*D+2048>>12;f=x;d=s+u;C=s-u;m=o+f;y=o-f;g=a+h;w=a-h;p=c+l;b=c-l;d<16?d=0:d>=4080?d=255:d>>=4;m<16?m=0:m>=4080?m=255:m>>=4;g<16?g=0:g>=4080?g=255:g>>=4;p<16?p=0:p>=4080?p=255:p>>=4;b<16?b=0:b>=4080?b=255:b>>=4;w<16?w=0:w>=4080?w=255:w>>=4;y<16?y=0:y>=4080?y=255:y>>=4;C<16?C=0:C>=4080?C=255:C>>=4;i[t+e]=d;i[t+e+8]=m;i[t+e+16]=g;i[t+e+24]=p;i[t+e+32]=b;i[t+e+40]=w;i[t+e+48]=y;i[t+e+56]=C}else{x=U*d+8192>>14;x=x<-2040?0:x>=2024?255:x+2056>>4;i[t+e]=x;i[t+e+8]=x;i[t+e+16]=x;i[t+e+24]=x;i[t+e+32]=x;i[t+e+40]=x;i[t+e+48]=x;i[t+e+56]=x}}}function buildComponentData(e,t){const n=t.blocksPerLine,r=t.blocksPerColumn,i=new Int16Array(64);for(let e=0;e<r;e++)for(let r=0;r<n;r++){quantizeAndInverse(t,getBlockBufferOffset(t,e,r),i)}return t.blockData}function findNextFileMarker(e,t,n=t){const r=e.length-1;let i=n<t?n:t;if(t>=r)return null;const s=readUint16(e,t);if(s>=65472&&s<=65534)return{invalid:null,marker:s,offset:t};let o=readUint16(e,i);for(;!(o>=65472&&o<=65534);){if(++i>=r)return null;o=readUint16(e,i)}return{invalid:s.toString(16),marker:o,offset:i}}function prepareComponents(e){const t=Math.ceil(e.samplesPerLine/8/e.maxH),n=Math.ceil(e.scanLines/8/e.maxV);for(const r of e.components){const i=Math.ceil(Math.ceil(e.samplesPerLine/8)*r.h/e.maxH),s=Math.ceil(Math.ceil(e.scanLines/8)*r.v/e.maxV),o=t*r.h,a=64*(n*r.v)*(o+1);r.blockData=new Int16Array(a);r.blocksPerLine=i;r.blocksPerColumn=s}e.mcusPerLine=t;e.mcusPerColumn=n}function readDataBlock(e,t){const n=readUint16(e,t);let r=(t+=2)+n-2;const i=findNextFileMarker(e,r,t);if(i?.invalid){util_warn("readDataBlock - incorrect length, current marker is: "+i.invalid);r=i.offset}const s=e.subarray(t,r);return{appData:s,oldOffset:t,newOffset:t+s.length}}function skipData(e,t){const n=readUint16(e,t),r=(t+=2)+n-2,i=findNextFileMarker(e,r,t);return i?.invalid?i.offset:r}class JpegImage{constructor({decodeTransform:e=null,colorTransform:t=-1}={}){this._decodeTransform=e;this._colorTransform=t}static canUseImageDecoder(e,t=-1){let n=null,r=0,i=null,s=readUint16(e,r);r+=2;if(65496!==s)throw new JpegError("SOI not found");s=readUint16(e,r);r+=2;e:for(;65497!==s;){switch(s){case 65505:const{appData:t,oldOffset:o,newOffset:a}=readDataBlock(e,r);r=a;if(69===t[0]&&120===t[1]&&105===t[2]&&102===t[3]&&0===t[4]&&0===t[5]){if(n)throw new JpegError("Duplicate EXIF-blocks found.");n={exifStart:o+6,exifEnd:a}}s=readUint16(e,r);r+=2;continue;case 65472:case 65473:case 65474:i=e[r+7];break e;case 65535:255!==e[r]&&r--}r=skipData(e,r);s=readUint16(e,r);r+=2}return 4===i||3===i&&0===t?null:n||{}}parse(e,{dnlScanLines:t=null}={}){let n,r,i=0,s=null,o=null,a=0;const c=[],l=[],h=[];let f=readUint16(e,i);i+=2;if(65496!==f)throw new JpegError("SOI not found");f=readUint16(e,i);i+=2;e:for(;65497!==f;){let u,d,m;switch(f){case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:const{appData:g,newOffset:p}=readDataBlock(e,i);i=p;65504===f&&74===g[0]&&70===g[1]&&73===g[2]&&70===g[3]&&0===g[4]&&(s={version:{major:g[5],minor:g[6]},densityUnits:g[7],xDensity:g[8]<<8|g[9],yDensity:g[10]<<8|g[11],thumbWidth:g[12],thumbHeight:g[13],thumbData:g.subarray(14,14+3*g[12]*g[13])});65518===f&&65===g[0]&&100===g[1]&&111===g[2]&&98===g[3]&&101===g[4]&&(o={version:g[5]<<8|g[6],flags0:g[7]<<8|g[8],flags1:g[9]<<8|g[10],transformCode:g[11]});break;case 65499:const b=readUint16(e,i);i+=2;const w=b+i-2;let y;for(;i<w;){const t=e[i++],n=new Uint16Array(64);if(t>>4){if(t>>4!=1)throw new JpegError("DQT - invalid table spec");for(d=0;d<64;d++){y=I[d];n[y]=readUint16(e,i);i+=2}}else for(d=0;d<64;d++){y=I[d];n[y]=e[i++]}c[15&t]=n}break;case 65472:case 65473:case 65474:if(n)throw new JpegError("Only single frame JPEGs supported");i+=2;n={};n.extended=65473===f;n.progressive=65474===f;n.precision=e[i++];const C=readUint16(e,i);i+=2;n.scanLines=t||C;n.samplesPerLine=readUint16(e,i);i+=2;n.components=[];n.componentIds={};const x=e[i++];let _=0,B=0;for(u=0;u<x;u++){const t=e[i],r=e[i+1]>>4,s=15&e[i+1];_<r&&(_=r);B<s&&(B=s);const o=e[i+2];m=n.components.push({h:r,v:s,quantizationId:o,quantizationTable:null});n.componentIds[t]=m-1;i+=3}n.maxH=_;n.maxV=B;prepareComponents(n);break;case 65476:const R=readUint16(e,i);i+=2;for(u=2;u<R;){const t=e[i++],n=new Uint8Array(16);let r=0;for(d=0;d<16;d++,i++)r+=n[d]=e[i];const s=new Uint8Array(r);for(d=0;d<r;d++,i++)s[d]=e[i];u+=17+r;(t>>4?l:h)[15&t]=buildHuffmanTable(n,s)}break;case 65501:i+=2;r=readUint16(e,i);i+=2;break;case 65498:const S=1==++a&&!t;i+=2;const A=e[i++],k=[];for(u=0;u<A;u++){const t=e[i++],r=n.componentIds[t],s=n.components[r];s.index=t;const o=e[i++];s.huffmanTableDC=h[o>>4];s.huffmanTableAC=l[15&o];k.push(s)}const v=e[i++],D=e[i++],M=e[i++];try{i+=decodeScan(e,i,n,k,r,v,D,M>>4,15&M,S)}catch(t){if(t instanceof DNLMarkerError){util_warn(`${t.message} -- attempting to re-parse the JPEG image.`);return this.parse(e,{dnlScanLines:t.scanLines})}if(t instanceof EOIMarkerError){util_warn(`${t.message} -- ignoring the rest of the image data.`);break e}throw t}break;case 65500:i+=4;break;case 65535:255!==e[i]&&i--;break;default:const T=findNextFileMarker(e,i-2,i-3);if(T?.invalid){util_warn("JpegImage.parse - unexpected data, current marker is: "+T.invalid);i=T.offset;break}if(!T||i>=e.length-1){util_warn("JpegImage.parse - reached the end of the image data without finding an EOI marker (0xFFD9).");break e}throw new JpegError("JpegImage.parse - unknown marker: "+f.toString(16))}f=readUint16(e,i);i+=2}if(!n)throw new JpegError("JpegImage.parse - no frame data found.");this.width=n.samplesPerLine;this.height=n.scanLines;this.jfif=s;this.adobe=o;this.components=[];for(const e of n.components){const t=c[e.quantizationId];t&&(e.quantizationTable=t);this.components.push({index:e.index,output:buildComponentData(0,e),scaleX:e.h/n.maxH,scaleY:e.v/n.maxV,blocksPerLine:e.blocksPerLine,blocksPerColumn:e.blocksPerColumn})}this.numComponents=this.components.length}_getLinearizedBlockData(e,t,n=!1){const r=this.width/e,i=this.height/t;let s,o,a,c,l,h,f,u,d,m,g,p=0;const b=this.components.length,w=e*t*b,y=new Uint8ClampedArray(w),C=new Uint32Array(e),x=4294967288;let _;for(f=0;f<b;f++){s=this.components[f];o=s.scaleX*r;a=s.scaleY*i;p=f;g=s.output;c=s.blocksPerLine+1<<3;if(o!==_){for(l=0;l<e;l++){u=0|l*o;C[l]=(u&x)<<3|7&u}_=o}for(h=0;h<t;h++){u=0|h*a;m=c*(u&x)|(7&u)<<3;for(l=0;l<e;l++){y[p]=g[m+C[l]];p+=b}}}let B=this._decodeTransform;n||4!==b||B||(B=new Int32Array([-256,255,-256,255,-256,255,-256,255]));if(B)for(f=0;f<w;)for(u=0,d=0;u<b;u++,f++,d+=2)y[f]=(y[f]*B[d]>>8)+B[d+1];return y}get _isColorConversionNeeded(){return this.adobe?!!this.adobe.transformCode:3===this.numComponents?0!==this._colorTransform&&(82!==this.components[0].index||71!==this.components[1].index||66!==this.components[2].index):1===this._colorTransform}_convertYccToRgb(e){let t,n,r;for(let i=0,s=e.length;i<s;i+=3){t=e[i];n=e[i+1];r=e[i+2];e[i]=t-179.456+1.402*r;e[i+1]=t+135.459-.344*n-.714*r;e[i+2]=t-226.816+1.772*n}return e}_convertYccToRgba(e,t){for(let n=0,r=0,i=e.length;n<i;n+=3,r+=4){const i=e[n],s=e[n+1],o=e[n+2];t[r]=i-179.456+1.402*o;t[r+1]=i+135.459-.344*s-.714*o;t[r+2]=i-226.816+1.772*s;t[r+3]=255}return t}_convertYcckToRgb(e){this._convertYcckToCmyk(e);return this._convertCmykToRgb(e)}_convertYcckToRgba(e){this._convertYcckToCmyk(e);return this._convertCmykToRgba(e)}_convertYcckToCmyk(e){let t,n,r;for(let i=0,s=e.length;i<s;i+=4){t=e[i];n=e[i+1];r=e[i+2];e[i]=434.456-t-1.402*r;e[i+1]=119.541-t+.344*n+.714*r;e[i+2]=481.816-t-1.772*n}return e}_convertCmykToRgb(e){const t=e.length/4;ColorSpaceUtils.cmyk.getRgbBuffer(e,0,t,e,0,8,0);return e.subarray(0,3*t)}_convertCmykToRgba(e){ColorSpaceUtils.cmyk.getRgbBuffer(e,0,e.length/4,e,0,8,1);if(ColorSpaceUtils.cmyk instanceof DeviceCmykCS)for(let t=3,n=e.length;t<n;t+=4)e[t]=255;return e}getData({width:e,height:t,forceRGBA:n=!1,forceRGB:r=!1,isSourcePDF:i=!1}){if(this.numComponents>4)throw new JpegError("Unsupported color mode");const s=this._getLinearizedBlockData(e,t,i);if(1===this.numComponents&&(n||r)){const e=s.length*(n?4:3),t=new Uint8ClampedArray(e);let r=0;if(n)!function grayToRGBA(e,t){if(util_FeatureTest.isLittleEndian)for(let n=0,r=e.length;n<r;n++)t[n]=65793*e[n]|4278190080;else for(let n=0,r=e.length;n<r;n++)t[n]=16843008*e[n]|255}(s,new Uint32Array(t.buffer));else for(const e of s){t[r++]=e;t[r++]=e;t[r++]=e}return t}if(3===this.numComponents&&this._isColorConversionNeeded){if(n){const e=new Uint8ClampedArray(s.length/3*4);return this._convertYccToRgba(s,e)}return this._convertYccToRgb(s)}if(4===this.numComponents){if(this._isColorConversionNeeded)return n?this._convertYcckToRgba(s):r?this._convertYcckToRgb(s):this._convertYcckToCmyk(s);if(n)return this._convertCmykToRgba(s);if(r)return this._convertCmykToRgb(s)}return s}}var OpenJPEG=async function(e={}){var t,n,r=e,i=new Promise(((e,r)=>{t=e;n=r})),s="./this.program",quit_=(e,t)=>{throw t},o=import.meta.url;try{new URL(".",o).href}catch{}var a,c,l,h,f,u,d=console.log.bind(console),m=console.error.bind(console),g=!1;function updateMemoryViews(){var e=a.buffer;l=new Int8Array(e);new Int16Array(e);h=new Uint8Array(e);new Uint16Array(e);f=new Int32Array(e);u=new Uint32Array(e);new Float32Array(e);new Float64Array(e);new BigInt64Array(e);new BigUint64Array(e)}var p=0,b=null;class ExitStatus{name="ExitStatus";constructor(e){this.message=`Program terminated with exit(${e})`;this.status=e}}var callRuntimeCallbacks=e=>{for(;e.length>0;)e.shift()(r)},w=[],addOnPostRun=e=>w.push(e),y=[],addOnPreRun=e=>y.push(e),C=!0,x=0,_={},handleException=e=>{if(e instanceof ExitStatus||"unwind"==e)return c;quit_(0,e)},keepRuntimeAlive=()=>C||x>0,_proc_exit=e=>{c=e;if(!keepRuntimeAlive()){r.onExit?.(e);g=!0}quit_(0,new ExitStatus(e))},_exit=(e,t)=>{c=e;_proc_exit(e)},callUserCallback=e=>{if(!g)try{e();(()=>{if(!keepRuntimeAlive())try{_exit(c)}catch(e){handleException(e)}})()}catch(e){handleException(e)}},growMemory=e=>{var t=(e-a.buffer.byteLength+65535)/65536|0;try{a.grow(t);updateMemoryViews();return 1}catch(e){}},B={},getEnvStrings=()=>{if(!getEnvStrings.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:s||"./this.program"};for(var t in B)void 0===B[t]?delete e[t]:e[t]=B[t];var n=[];for(var t in e)n.push(`${t}=${e[t]}`);getEnvStrings.strings=n}return getEnvStrings.strings},lengthBytesUTF8=e=>{for(var t=0,n=0;n<e.length;++n){var r=e.charCodeAt(n);if(r<=127)t++;else if(r<=2047)t+=2;else if(r>=55296&&r<=57343){t+=4;++n}else t+=3}return t},R=[null,[],[]],S="undefined"!=typeof TextDecoder?new TextDecoder:void 0,UTF8ArrayToString=(e,t=0,n=NaN)=>{for(var r=t+n,i=t;e[i]&&!(i>=r);)++i;if(i-t>16&&e.buffer&&S)return S.decode(e.subarray(t,i));for(var s="";t<i;){var o=e[t++];if(128&o){var a=63&e[t++];if(192!=(224&o)){var c=63&e[t++];if((o=224==(240&o)?(15&o)<<12|a<<6|c:(7&o)<<18|a<<12|c<<6|63&e[t++])<65536)s+=String.fromCharCode(o);else{var l=o-65536;s+=String.fromCharCode(55296|l>>10,56320|1023&l)}}else s+=String.fromCharCode((31&o)<<6|a)}else s+=String.fromCharCode(o)}return s},printChar=(e,t)=>{var n=R[e];if(0===t||10===t){(1===e?d:m)(UTF8ArrayToString(n));n.length=0}else n.push(t)},UTF8ToString=(e,t)=>e?UTF8ArrayToString(h,e,t):"";r.noExitRuntime&&(C=r.noExitRuntime);r.print&&(d=r.print);r.printErr&&(m=r.printErr);r.wasmBinary&&r.wasmBinary;r.arguments&&r.arguments;r.thisProgram&&(s=r.thisProgram);r.writeArrayToMemory=(e,t)=>{l.set(e,t)};var A={l:()=>function abort(e){r.onAbort?.(e);m(e="Aborted("+e+")");g=!0;e+=". Build with -sASSERTIONS for more info.";var t=new WebAssembly.RuntimeError(e);n(t);throw t}(""),k:()=>{C=!1;x=0},m:(e,t)=>{if(_[e]){clearTimeout(_[e].id);delete _[e]}if(!t)return 0;var n=setTimeout((()=>{delete _[e];callUserCallback((()=>I(e,performance.now())))}),t);_[e]={id:n,timeout_ms:t};return 0},g:function _copy_pixels_1(e,t){e>>=2;const n=r.imageData=new Uint8ClampedArray(t),i=f.subarray(e,e+t);n.set(i)},f:function _copy_pixels_3(e,t,n,i){e>>=2;t>>=2;n>>=2;const s=r.imageData=new Uint8ClampedArray(3*i),o=f.subarray(e,e+i),a=f.subarray(t,t+i),c=f.subarray(n,n+i);for(let e=0;e<i;e++){s[3*e]=o[e];s[3*e+1]=a[e];s[3*e+2]=c[e]}},e:function _copy_pixels_4(e,t,n,i,s){e>>=2;t>>=2;n>>=2;i>>=2;const o=r.imageData=new Uint8ClampedArray(4*s),a=f.subarray(e,e+s),c=f.subarray(t,t+s),l=f.subarray(n,n+s),h=f.subarray(i,i+s);for(let e=0;e<s;e++){o[4*e]=a[e];o[4*e+1]=c[e];o[4*e+2]=l[e];o[4*e+3]=h[e]}},n:e=>{var t,n,r=h.length,i=2147483648;if((e>>>=0)>i)return!1;for(var s=1;s<=4;s*=2){var o=r*(1+.2/s);o=Math.min(o,e+100663296);var a=Math.min(i,(t=Math.max(e,o),n=65536,Math.ceil(t/n)*n));if(growMemory(a))return!0}return!1},p:(e,t)=>{var n=0,r=0;for(var i of getEnvStrings()){var s=t+n;u[e+r>>2]=s;n+=((e,t,n,r)=>{if(!(r>0))return 0;for(var i=n,s=n+r-1,o=0;o<e.length;++o){var a=e.charCodeAt(o);a>=55296&&a<=57343&&(a=65536+((1023&a)<<10)|1023&e.charCodeAt(++o));if(a<=127){if(n>=s)break;t[n++]=a}else if(a<=2047){if(n+1>=s)break;t[n++]=192|a>>6;t[n++]=128|63&a}else if(a<=65535){if(n+2>=s)break;t[n++]=224|a>>12;t[n++]=128|a>>6&63;t[n++]=128|63&a}else{if(n+3>=s)break;t[n++]=240|a>>18;t[n++]=128|a>>12&63;t[n++]=128|a>>6&63;t[n++]=128|63&a}}t[n]=0;return n-i})(i,h,s,1/0)+1;r+=4}return 0},q:(e,t)=>{var n=getEnvStrings();u[e>>2]=n.length;var r=0;for(var i of n)r+=lengthBytesUTF8(i)+1;u[t>>2]=r;return 0},b:e=>52,o:function _fd_seek(e,t,n,r){t=(i=t)<-9007199254740992||i>9007199254740992?NaN:Number(i);var i;return 70},c:(e,t,n,r)=>{for(var i=0,s=0;s<n;s++){var o=u[t>>2],a=u[t+4>>2];t+=8;for(var c=0;c<a;c++)printChar(e,h[o+c]);i+=a}u[r>>2]=i;return 0},r:function _gray_to_rgba(e,t){e>>=2;const n=r.imageData=new Uint8ClampedArray(4*t),i=f.subarray(e,e+t);for(let e=0;e<t;e++){n[4*e]=n[4*e+1]=n[4*e+2]=i[e];n[4*e+3]=255}},i:function _graya_to_rgba(e,t,n){e>>=2;t>>=2;const i=r.imageData=new Uint8ClampedArray(4*n),s=f.subarray(e,e+n),o=f.subarray(t,t+n);for(let e=0;e<n;e++){i[4*e]=i[4*e+1]=i[4*e+2]=s[e];i[4*e+3]=o[e]}},d:function _jsPrintWarning(e){const t=UTF8ToString(e);(r.warn||console.warn)(`OpenJPEG: ${t}`)},j:_proc_exit,h:function _rgb_to_rgba(e,t,n,i){e>>=2;t>>=2;n>>=2;const s=r.imageData=new Uint8ClampedArray(4*i),o=f.subarray(e,e+i),a=f.subarray(t,t+i),c=f.subarray(n,n+i);for(let e=0;e<i;e++){s[4*e]=o[e];s[4*e+1]=a[e];s[4*e+2]=c[e];s[4*e+3]=255}},a:function _storeErrorMessage(e){const t=UTF8ToString(e);r.errorMessages?r.errorMessages+="\n"+t:r.errorMessages=t}},k=await async function createWasm(){function receiveInstance(e,t){k=e.exports;a=k.s;updateMemoryViews();!function removeRunDependency(e){p--;r.monitorRunDependencies?.(p);if(0==p&&b){var t=b;b=null;t()}}();return k}!function addRunDependency(e){p++;r.monitorRunDependencies?.(p)}();var e=function getWasmImports(){return{a:A}}();return new Promise(((t,n)=>{r.instantiateWasm(e,((e,n)=>{t(receiveInstance(e))}))}))}(),I=(k.t,r._malloc=k.u,r._free=k.v,r._jp2_decode=k.w,k.x);!function preInit(){if(r.preInit){"function"==typeof r.preInit&&(r.preInit=[r.preInit]);for(;r.preInit.length>0;)r.preInit.shift()()}}();!function run(){if(p>0)b=run;else{!function preRun(){if(r.preRun){"function"==typeof r.preRun&&(r.preRun=[r.preRun]);for(;r.preRun.length;)addOnPreRun(r.preRun.shift())}callRuntimeCallbacks(y)}();if(p>0)b=run;else if(r.setStatus){r.setStatus("Running...");setTimeout((()=>{setTimeout((()=>r.setStatus("")),1);doRun()}),1)}else doRun()}function doRun(){r.calledRun=!0;if(!g){!function initRuntime(){k.t()}();t(r);r.onRuntimeInitialized?.();!function postRun(){if(r.postRun){"function"==typeof r.postRun&&(r.postRun=[r.postRun]);for(;r.postRun.length;)addOnPostRun(r.postRun.shift())}callRuntimeCallbacks(w)}()}}}();return i};const F=OpenJPEG;class Stream extends base_stream_BaseStream{constructor(e,t,n,r){super();this.bytes=e instanceof Uint8Array?e:new Uint8Array(e);this.start=t||0;this.pos=this.start;this.end=t+n||this.bytes.length;this.dict=r}get length(){return this.end-this.start}get isEmpty(){return 0===this.length}getByte(){return this.pos>=this.end?-1:this.bytes[this.pos++]}getBytes(e){const t=this.bytes,n=this.pos,r=this.end;if(!e)return t.subarray(n,r);let i=n+e;i>r&&(i=r);this.pos=i;return t.subarray(n,i)}getByteRange(e,t){e<0&&(e=0);t>this.end&&(t=this.end);return this.bytes.subarray(e,t)}reset(){this.pos=this.start}moveStart(){this.start=this.pos}makeSubStream(e,t,n=null){return new Stream(this.bytes.buffer,e,t,n)}}class JpxError extends n{constructor(e){super(e,"JpxError")}}class JpxImage{static#M=null;static#T=null;static#P=null;static#S=!0;static#E=!0;static#A=null;static setOptions({handler:e,useWasm:t,useWorkerFetch:n,wasmUrl:r}){this.#S=t;this.#E=n;this.#A=r;n||(this.#T=e)}static async#U(e){const t=`${this.#A}openjpeg_nowasm_fallback.js`;let n=null;try{n=(await import(
/*webpackIgnore: true*/
/*@vite-ignore*/
t)).default()}catch(e){util_warn(`JpxImage#getJsModule: ${e}`)}e(n)}static async#L(e,t,n){const r="openjpeg.wasm";try{this.#M||(this.#E?this.#M=await async function fetchBinaryData(e){const t=await fetch(e);if(!t.ok)throw new Error(`Failed to fetch file "${e}" with "${t.statusText}".`);return new Uint8Array(await t.arrayBuffer())}(`${this.#A}${r}`):this.#M=await this.#T.sendWithPromise("FetchBinaryData",{type:"wasmFactory",filename:r}));return n((await WebAssembly.instantiate(this.#M,t)).instance)}catch(t){util_warn(`JpxImage#instantiateWasm: ${t}`);this.#U(e);return null}finally{this.#T=null}}static async decode(e,{numComponents:t=4,isIndexedColormap:n=!1,smaskInData:r=!1,reducePower:i=0}={}){if(!this.#P){const{promise:e,resolve:t}=Promise.withResolvers(),n=[e];this.#S?n.push(F({warn:util_warn,instantiateWasm:this.#L.bind(this,t)})):this.#U(t);this.#P=Promise.race(n)}const s=await this.#P;if(!s)throw new JpxError("OpenJPEG failed to initialize");let o;try{const a=e.length;o=s._malloc(a);s.writeArrayToMemory(e,o);if(s._jp2_decode(o,a,t>0?t:0,!!n,!!r,i)){const{errorMessages:e}=s;if(e){delete s.errorMessages;throw new JpxError(e)}throw new JpxError("Unknown error")}const{imageData:c}=s;s.imageData=null;return c}finally{o&&s._free(o)}}static cleanup(){this.#P=null}static parseImageProperties(e){if(!(e instanceof ArrayBuffer||ArrayBuffer.isView(e)))throw new JpxError("Invalid data format, must be a TypedArray.");let t=(e=new Stream(e)).getByte();for(;t>=0;){const n=t;t=e.getByte();if(65361===(n<<8|t)){e.skip(4);const t=e.getInt32()>>>0,n=e.getInt32()>>>0,r=e.getInt32()>>>0,i=e.getInt32()>>>0;e.skip(16);return{width:t-r,height:n-i,bitsPerComponent:8,componentsCount:e.getUint16()}}}throw new JpxError("No size marker found in JPX stream")}}globalThis.pdfjsImageDecoders={getVerbosityLevel,Jbig2Error,Jbig2Image,JpegError,JpegImage,JpxError,JpxImage,setVerbosityLevel,VerbosityLevel:e};export{Jbig2Error,Jbig2Image,JpegError,JpegImage,JpxError,JpxImage,e as VerbosityLevel,getVerbosityLevel,setVerbosityLevel};
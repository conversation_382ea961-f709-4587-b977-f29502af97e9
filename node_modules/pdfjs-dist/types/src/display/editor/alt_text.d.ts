export class AltText {
    static "__#40@#l10nNewButton": null;
    static _l10n: null;
    static initialize(l10n: any): void;
    constructor(editor: any);
    render(): Promise<HTMLButtonElement>;
    finish(): void;
    isEmpty(): boolean;
    hasData(): boolean;
    get guessedText(): null;
    setGuessedText(guessedText: any): Promise<void>;
    toggleAltTextBadge(visibility?: boolean): void;
    serialize(isForCopying: any): {
        altText: null;
        decorative: boolean;
        guessedText: null;
        textWithDisclaimer: null;
    };
    /**
     * Set the alt text data.
     */
    set data({ altText, decorative, guessedText, textWithDisclaimer, cancel, }: {
        altText: null;
        decorative: boolean;
    });
    get data(): {
        altText: null;
        decorative: boolean;
    };
    toggle(enabled?: boolean): void;
    shown(): void;
    destroy(): void;
    #private;
}

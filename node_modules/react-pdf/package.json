{"name": "react-pdf", "version": "10.0.1", "description": "Display PDFs in your React app as easily as if they were images.", "type": "module", "sideEffects": ["*.css"], "main": "./dist/index.js", "source": "./src/index.ts", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js", "./*": "./*"}, "scripts": {"build": "yarn build-js && yarn copy-styles", "build-js": "tsc --project tsconfig.build.json", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true })\"", "copy-styles": "cpy 'src/**/*.css' dist", "format": "biome format", "lint": "biome lint", "prepack": "yarn clean && yarn build", "test": "yarn lint && yarn tsc && yarn format && yarn unit", "tsc": "tsc", "unit": "vitest", "watch": "yarn build-js --watch & node --eval \"fs.watch('src', () => child_process.exec('yarn copy-styles'))\""}, "keywords": ["pdf", "pdf-viewer", "react"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"clsx": "^2.0.0", "dequal": "^2.0.3", "make-cancellable-promise": "^2.0.0", "make-event-props": "^2.0.0", "merge-refs": "^2.0.0", "pdfjs-dist": "5.3.31", "tiny-invariant": "^1.0.0", "warning": "^4.0.0"}, "devDependencies": {"@biomejs/biome": "2.0.0", "@testing-library/dom": "^10.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^16.0.0", "@types/node": "*", "@types/react": "*", "@types/react-dom": "*", "@types/warning": "^3.0.0", "@vitest/browser": "^3.2.3", "cpy-cli": "^5.0.0", "playwright": "^1.51.1", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.5.2", "vitest": "^3.2.3", "vitest-browser-react": "^0.2.0"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "publishConfig": {"access": "public", "provenance": true}, "files": ["dist", "src"], "repository": {"type": "git", "url": "git+https://github.com/wojtekmaj/react-pdf.git", "directory": "packages/react-pdf"}, "funding": "https://github.com/wojtekmaj/react-pdf?sponsor=1"}
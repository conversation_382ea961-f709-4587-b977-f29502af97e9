var r=require("react");function n(n,e,u,t){var c=this,i=r.useRef(null),a=r.useRef(0),f=r.useRef(0),o=r.useRef(null),l=r.useRef([]),s=r.useRef(),v=r.useRef(),d=r.useRef(n),m=r.useRef(!0);d.current=n;var R="undefined"!=typeof window,p=!e&&0!==e&&R;if("function"!=typeof n)throw new TypeError("Expected a function");e=+e||0;var g=!!(u=u||{}).leading,x=!("trailing"in u)||!!u.trailing,b="maxWait"in u,w="debounceOnServer"in u&&!!u.debounceOnServer,h=b?Math.max(+u.maxWait||0,e):null;r.useEffect(function(){return m.current=!0,function(){m.current=!1}},[]);var y=r.useMemo(function(){var r=function(r){var n=l.current,e=s.current;return l.current=s.current=null,a.current=r,f.current=f.current||r,v.current=d.current.apply(e,n)},n=function(r,n){p&&cancelAnimationFrame(o.current),o.current=p?requestAnimationFrame(r):setTimeout(r,n)},u=function(r){if(!m.current)return!1;var n=r-i.current;return!i.current||n>=e||n<0||b&&r-a.current>=h},y=function(n){return o.current=null,x&&l.current?r(n):(l.current=s.current=null,v.current)},D=function r(){var t=Date.now();if(g&&f.current===a.current&&F(),u(t))return y(t);if(m.current){var c=e-(t-i.current),o=b?Math.min(c,h-(t-a.current)):c;n(r,o)}},F=function(){t&&t({})},T=function(){if(R||w){var t=Date.now(),f=u(t);if(l.current=[].slice.call(arguments),s.current=c,i.current=t,f){if(!o.current&&m.current)return a.current=i.current,n(D,e),g?r(i.current):v.current;if(b)return n(D,e),r(i.current)}return o.current||n(D,e),v.current}};return T.cancel=function(){o.current&&(p?cancelAnimationFrame(o.current):clearTimeout(o.current)),a.current=0,l.current=i.current=s.current=o.current=null},T.isPending=function(){return!!o.current},T.flush=function(){return o.current?y(Date.now()):v.current},T},[g,b,e,h,x,p,R,w,t]);return y}function e(r,n){return r===n}exports.useDebounce=function(u,t,c){var i=c&&c.equalityFn||e,a=r.useRef(u),f=r.useState({})[1],o=n(r.useCallback(function(r){a.current=r,f({})},[f]),t,c,f),l=r.useRef(u);return i(l.current,u)||(o(u),l.current=u),[a.current,o]},exports.useDebouncedCallback=n,exports.useThrottledCallback=function(r,e,u){var t=void 0===u?{}:u,c=t.leading,i=t.trailing;return n(r,e,{maxWait:e,leading:void 0===c||c,trailing:void 0===i||i})};
//# sourceMappingURL=index.js.map

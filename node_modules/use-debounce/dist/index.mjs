import{useRef as r,useEffect as n,useMemo as t,useState as e,useCallback as u}from"react";function c(e,u,c,i){const o=r(null),a=r(0),l=r(0),f=r(null),s=r([]),m=r(),d=r(),g=r(e),p=r(!0);g.current=e;const w="undefined"!=typeof window,x=!u&&0!==u&&w;if("function"!=typeof e)throw new TypeError("Expected a function");u=+u||0;const h=!!(c=c||{}).leading,y=!("trailing"in c)||!!c.trailing,F="maxWait"in c,A="debounceOnServer"in c&&!!c.debounceOnServer,D=F?Math.max(+c.maxWait||0,u):null;n(()=>(p.current=!0,()=>{p.current=!1}),[]);const T=t(()=>{const r=r=>{const n=s.current,t=m.current;return s.current=m.current=null,a.current=r,l.current=l.current||r,d.current=g.current.apply(t,n)},n=(r,n)=>{x&&cancelAnimationFrame(f.current),f.current=x?requestAnimationFrame(r):setTimeout(r,n)},t=r=>{if(!p.current)return!1;const n=r-o.current;return!o.current||n>=u||n<0||F&&r-a.current>=D},e=n=>(f.current=null,y&&s.current?r(n):(s.current=m.current=null,d.current)),c=()=>{const r=Date.now();if(h&&l.current===a.current&&T(),t(r))return e(r);if(!p.current)return;const i=u-(r-o.current),f=F?Math.min(i,D-(r-a.current)):i;n(c,f)},T=()=>{i&&i({})},W=(...e)=>{if(!w&&!A)return;const i=Date.now(),l=t(i);if(s.current=e,m.current=this,o.current=i,l){if(!f.current&&p.current)return a.current=o.current,n(c,u),h?r(o.current):d.current;if(F)return n(c,u),r(o.current)}return f.current||n(c,u),d.current};return W.cancel=()=>{f.current&&(x?cancelAnimationFrame(f.current):clearTimeout(f.current)),a.current=0,s.current=o.current=m.current=f.current=null},W.isPending=()=>!!f.current,W.flush=()=>f.current?e(Date.now()):d.current,W},[h,F,u,D,y,x,w,A,i]);return T}function i(r,n){return r===n}function o(n,t,o){const a=o&&o.equalityFn||i,l=r(n),[,f]=e({}),s=c(u(r=>{l.current=r,f({})},[f]),t,o,f),m=r(n);return a(m.current,n)||(s(n),m.current=n),[l.current,s]}function a(r,n,{leading:t=!0,trailing:e=!0}={}){return c(r,n,{maxWait:n,leading:t,trailing:e})}export{o as useDebounce,c as useDebouncedCallback,a as useThrottledCallback};
//# sourceMappingURL=index.mjs.map

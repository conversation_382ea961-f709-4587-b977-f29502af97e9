!function(r,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],e):e((r||self)["use-debounce"]={},r.react)}(this,function(r,e){function n(r,n,t,u){var c=this,i=e.useRef(null),a=e.useRef(0),f=e.useRef(0),o=e.useRef(null),l=e.useRef([]),s=e.useRef(),d=e.useRef(),v=e.useRef(r),m=e.useRef(!0);v.current=r;var p="undefined"!=typeof window,b=!n&&0!==n&&p;if("function"!=typeof r)throw new TypeError("Expected a function");n=+n||0;var R=!!(t=t||{}).leading,g=!("trailing"in t)||!!t.trailing,h="maxWait"in t,y="debounceOnServer"in t&&!!t.debounceOnServer,x=h?Math.max(+t.maxWait||0,n):null;e.useEffect(function(){return m.current=!0,function(){m.current=!1}},[]);var w=e.useMemo(function(){var r=function(r){var e=l.current,n=s.current;return l.current=s.current=null,a.current=r,f.current=f.current||r,d.current=v.current.apply(n,e)},e=function(r,e){b&&cancelAnimationFrame(o.current),o.current=b?requestAnimationFrame(r):setTimeout(r,e)},t=function(r){if(!m.current)return!1;var e=r-i.current;return!i.current||e>=n||e<0||h&&r-a.current>=x},w=function(e){return o.current=null,g&&l.current?r(e):(l.current=s.current=null,d.current)},T=function r(){var u=Date.now();if(R&&f.current===a.current&&D(),t(u))return w(u);if(m.current){var c=n-(u-i.current),o=h?Math.min(c,x-(u-a.current)):c;e(r,o)}},D=function(){u&&u({})},F=function(){if(p||y){var u=Date.now(),f=t(u);if(l.current=[].slice.call(arguments),s.current=c,i.current=u,f){if(!o.current&&m.current)return a.current=i.current,e(T,n),R?r(i.current):d.current;if(h)return e(T,n),r(i.current)}return o.current||e(T,n),d.current}};return F.cancel=function(){o.current&&(b?cancelAnimationFrame(o.current):clearTimeout(o.current)),a.current=0,l.current=i.current=s.current=o.current=null},F.isPending=function(){return!!o.current},F.flush=function(){return o.current?w(Date.now()):d.current},F},[R,h,n,x,g,b,p,y,u]);return w}function t(r,e){return r===e}r.useDebounce=function(r,u,c){var i=c&&c.equalityFn||t,a=e.useRef(r),f=e.useState({})[1],o=n(e.useCallback(function(r){a.current=r,f({})},[f]),u,c,f),l=e.useRef(r);return i(l.current,r)||(o(r),l.current=r),[a.current,o]},r.useDebouncedCallback=n,r.useThrottledCallback=function(r,e,t){var u=void 0===t?{}:t,c=u.leading,i=u.trailing;return n(r,e,{maxWait:e,leading:void 0===c||c,trailing:void 0===i||i})}});
//# sourceMappingURL=index.umd.js.map

{"name": "warning", "version": "4.0.3", "description": "A mirror of Facebook's Warning", "main": "warning.js", "scripts": {"test": "npm run test:dev && npm run test:prod", "test:dev": "NODE_ENV=development jest", "test:prod": "NODE_ENV=production jest", "commit": "git cz", "commitmsg": "commitlint -e $GIT_PARAMS"}, "dependencies": {"loose-envify": "^1.0.0"}, "devDependencies": {"@commitlint/cli": "^6.2.0", "@commitlint/config-conventional": "^6.1.3", "browserify": "^16.2.2", "commitizen": "^2.10.1", "cz-conventional-changelog": "^2.1.0", "husky": "^0.14.3", "jest": "^23.1.0", "uglify-js": "^3.3.25"}, "repository": {"type": "git", "url": "https://github.com/BerkeleyTrue/warning.git"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "browserify": {"transform": ["loose-envify"]}, "files": ["warning.js"], "keywords": ["warning", "facebook", "react", "invariant"], "author": "<PERSON> <<EMAIL>> (http://www.berkeleytrue.com)", "license": "MIT", "bugs": {"url": "https://github.com/BerkeleyTrue/warning/issues"}, "homepage": "https://github.com/BerkeleyTrue/warning"}